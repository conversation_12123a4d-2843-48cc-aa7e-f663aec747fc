import { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { motion } from 'framer-motion'
import { 
  CheckIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'

interface PlanSummary {
  title: string
  description: string
  key_tactics: string[]
  estimated_cost: number
  estimated_time_hours: number
  success_probability: number
}

export default function PlanSummaryPage() {
  const [planSummary, setPlanSummary] = useState<PlanSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Get plan summary from localStorage
    const storedSummary = localStorage.getItem('planSummary')
    const storedAnswers = localStorage.getItem('quizAnswers')
    
    if (!storedSummary || !storedAnswers) {
      router.push('/quiz')
      return
    }

    try {
      const summary = JSON.parse(storedSummary)
      setPlanSummary(summary)
    } catch (error) {
      console.error('Error parsing plan summary:', error)
      router.push('/quiz')
    } finally {
      setLoading(false)
    }
  }, [router])

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحضير خطتك...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (!planSummary) {
    return null
  }

  return (
    <>
      <Head>
        <title>ملخص خطة النمو - Growlytics</title>
        <meta name="description" content="ملخص خطة النمو المخصصة لمشروعك" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <SparklesIcon className="w-8 h-8 text-primary-600" />
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                🎉 خطتك جاهزة!
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                تم إنشاء خطة نمو مخصصة خصيصاً لمشروعك. أنشئ حسابك الآن لحفظ التفاصيل والبدء في التنفيذ.
              </p>
            </motion.div>

            {/* Plan Summary Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="card mb-8"
            >
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {planSummary.title}
                </h2>
                <p className="text-gray-600 text-lg">
                  {planSummary.description}
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <CurrencyDollarIcon className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    ${planSummary.estimated_cost}
                  </div>
                  <div className="text-sm text-gray-600">التكلفة المتوقعة</div>
                </div>
                
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <ClockIcon className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {planSummary.estimated_time_hours} ساعة
                  </div>
                  <div className="text-sm text-gray-600">الوقت المطلوب</div>
                </div>
                
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <ChartBarIcon className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {Math.round(planSummary.success_probability * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">احتمالية النجاح</div>
                </div>
              </div>

              {/* Key Tactics */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  التكتيكات الرئيسية:
                </h3>
                <div className="space-y-3">
                  {planSummary.key_tactics.map((tactic, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.1 * index }}
                      className="flex items-center space-x-3 p-3 bg-primary-50 rounded-lg"
                    >
                      <CheckIcon className="w-5 h-5 text-primary-600 flex-shrink-0" />
                      <span className="text-gray-800">{tactic}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* CTA Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="card text-center gradient-bg text-white"
            >
              <h3 className="text-2xl font-bold mb-4">
                جاهز لبدء التنفيذ؟
              </h3>
              <p className="text-white/90 mb-6 text-lg">
                أنشئ حسابك المجاني الآن للحصول على الخطة الكاملة مع التفاصيل والإرشادات خطوة بخطوة
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/register">
                  <Button size="lg" variant="secondary" className="group">
                    إنشاء حساب مجاني
                    <ArrowRightIcon className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                
                <Link href="/auth/login">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                    لدي حساب بالفعل
                  </Button>
                </Link>
              </div>
              
              <p className="text-white/80 text-sm mt-4">
                ✨ مجاني تماماً • لا حاجة لبطاقة ائتمان • ابدأ فوراً
              </p>
            </motion.div>

            {/* Features Preview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mt-12"
            >
              <h3 className="text-xl font-semibold text-gray-900 text-center mb-8">
                ماذا ستحصل عليه بعد إنشاء الحساب؟
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckIcon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">خطة مفصلة</h4>
                  <p className="text-gray-600 text-sm">خطوات واضحة ومفصلة لكل تكتيك مع الأدوات المطلوبة</p>
                </div>
                
                <div className="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <SparklesIcon className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">مساعد ذكي</h4>
                  <p className="text-gray-600 text-sm">مساعد AI يجيب على أسئلتك ويساعدك في التنفيذ</p>
                </div>
                
                <div className="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <ChartBarIcon className="w-6 h-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">تتبع التقدم</h4>
                  <p className="text-gray-600 text-sm">تتبع تقدمك وقياس نتائج كل تكتيك</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </Layout>
    </>
  )
}
