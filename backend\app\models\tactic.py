from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class TacticType(str, Enum):
    ORGANIC = "organic"
    PAID = "paid"
    CONTENT = "content"
    LEAD = "lead"
    REPORTING = "reporting"

class TacticLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

class TacticCost(str, Enum):
    FREE = "free"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class TacticChannel(str, Enum):
    SOCIAL_MEDIA = "social_media"
    EMAIL = "email"
    SEO = "seo"
    PPC = "ppc"
    CONTENT_MARKETING = "content_marketing"
    INFLUENCER = "influencer"
    PARTNERSHIPS = "partnerships"
    DIRECT = "direct"
    OTHER = "other"

class TacticBase(BaseModel):
    title: str
    description: str
    short_description: str
    tactic_type: TacticType
    level: TacticLevel
    cost: TacticCost
    channel: TacticChannel
    estimated_time_hours: int
    estimated_cost_usd: float
    tools_required: List[str] = []
    steps: List[str] = []
    expected_outcome: str
    tags: List[str] = []
    business_stages: List[str] = []  # Which business stages this tactic is suitable for
    success_metrics: List[str] = []

class TacticCreate(TacticBase):
    pass

class TacticUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    short_description: Optional[str] = None
    tactic_type: Optional[TacticType] = None
    level: Optional[TacticLevel] = None
    cost: Optional[TacticCost] = None
    channel: Optional[TacticChannel] = None
    estimated_time_hours: Optional[int] = None
    estimated_cost_usd: Optional[float] = None
    tools_required: Optional[List[str]] = None
    steps: Optional[List[str]] = None
    expected_outcome: Optional[str] = None
    tags: Optional[List[str]] = None
    business_stages: Optional[List[str]] = None
    success_metrics: Optional[List[str]] = None

class TacticInDB(TacticBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool = True
    usage_count: int = 0
    rating: Optional[float] = None

class Tactic(TacticInDB):
    pass

class TacticResponse(BaseModel):
    id: str
    title: str
    description: str
    short_description: str
    tactic_type: TacticType
    level: TacticLevel
    cost: TacticCost
    channel: TacticChannel
    estimated_time_hours: int
    estimated_cost_usd: float
    tools_required: List[str]
    steps: List[str]
    expected_outcome: str
    tags: List[str]
    business_stages: List[str]
    success_metrics: List[str]
    created_at: datetime
    is_active: bool
    usage_count: int
    rating: Optional[float] = None

# Plan-Tactic relationship models
class PlanTacticBase(BaseModel):
    plan_id: str
    tactic_id: str
    priority: int = 1
    status: str = "pending"  # pending, in_progress, completed, skipped
    notes: Optional[str] = None

class PlanTacticCreate(PlanTacticBase):
    pass

class PlanTacticUpdate(BaseModel):
    priority: Optional[int] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class PlanTacticInDB(PlanTacticBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

class PlanTactic(PlanTacticInDB):
    tactic: Optional[TacticResponse] = None

# Tactic filters for search
class TacticFilters(BaseModel):
    tactic_type: Optional[TacticType] = None
    level: Optional[TacticLevel] = None
    cost: Optional[TacticCost] = None
    channel: Optional[TacticChannel] = None
    business_stage: Optional[str] = None
    max_time_hours: Optional[int] = None
    max_cost_usd: Optional[float] = None
    tags: Optional[List[str]] = None
    search_query: Optional[str] = None
