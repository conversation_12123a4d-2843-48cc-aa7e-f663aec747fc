from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class BusinessStage(str, Enum):
    IDEA = "idea"
    LAUNCH = "launch"
    PROFIT = "profit"
    GROWTH = "growth"
    SCALE = "scale"

class GrowthSpeed(str, Enum):
    SLOW = "slow"
    MEDIUM = "medium"
    FAST = "fast"

class MainChallenge(str, Enum):
    TRAFFIC = "traffic"
    CONVERSION = "conversion"
    RETENTION = "retention"
    CONTENT = "content"
    UNKNOWN = "unknown"

class TimeCommitment(str, Enum):
    LESS_THAN_2 = "less_than_2"
    TWO_TO_FIVE = "2_to_5"
    FIVE_TO_TEN = "5_to_10"
    FULL_TIME = "full_time"

class Budget(str, Enum):
    LESS_THAN_100 = "less_than_100"
    HUNDRED_TO_500 = "100_to_500"
    FIVE_HUNDRED_TO_1000 = "500_to_1000"
    MORE_THAN_1000 = "more_than_1000"

class BusinessProfile(BaseModel):
    business_type: str
    business_stage: BusinessStage
    growth_speed: GrowthSpeed
    main_challenge: MainChallenge
    time_commitment: TimeCommitment
    budget: Budget
    business_description: str
    primary_goal: str
    uploaded_files: Optional[List[str]] = []

class GrowthPlanBase(BaseModel):
    title: str
    description: Optional[str] = None
    business_profile: BusinessProfile
    estimated_cost: Optional[float] = None
    estimated_time_hours: Optional[int] = None
    priority_score: Optional[float] = None

class GrowthPlanCreate(GrowthPlanBase):
    user_id: str

class GrowthPlanUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    estimated_cost: Optional[float] = None
    estimated_time_hours: Optional[int] = None
    priority_score: Optional[float] = None

class GrowthPlanInDB(GrowthPlanBase):
    id: str
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool = True

class GrowthPlan(GrowthPlanInDB):
    tactics: Optional[List[Dict[str, Any]]] = []

class GrowthPlanResponse(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    business_profile: BusinessProfile
    estimated_cost: Optional[float] = None
    estimated_time_hours: Optional[int] = None
    priority_score: Optional[float] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool
    tactics_count: int = 0

class QuizAnswers(BaseModel):
    business_type: str
    business_stage: BusinessStage
    growth_speed: GrowthSpeed
    main_challenge: MainChallenge
    time_commitment: TimeCommitment
    budget: Budget
    business_description: str
    primary_goal: str

class GrowthPlanSummary(BaseModel):
    title: str
    description: str
    key_tactics: List[str]
    estimated_cost: float
    estimated_time_hours: int
    success_probability: float
