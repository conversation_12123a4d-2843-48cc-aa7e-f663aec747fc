// Common types used across the application

export interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'user' | 'admin' | 'consultant'
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  email_verified: boolean
  avatar_url?: string
}

export interface BusinessProfile {
  business_type: string
  business_stage: 'idea' | 'launch' | 'profit' | 'growth' | 'scale'
  growth_speed: 'slow' | 'medium' | 'fast'
  main_challenge: 'traffic' | 'conversion' | 'retention' | 'content' | 'unknown'
  time_commitment: 'less_than_2' | '2_to_5' | '5_to_10' | 'full_time'
  budget: 'less_than_100' | '100_to_500' | '500_to_1000' | 'more_than_1000'
  business_description: string
  primary_goal: string
  uploaded_files?: string[]
}

export interface GrowthPlan {
  id: string
  title: string
  description?: string
  business_profile: BusinessProfile
  estimated_cost?: number
  estimated_time_hours?: number
  priority_score?: number
  created_at: string
  updated_at?: string
  is_active: boolean
  tactics_count: number
}

export interface Tactic {
  id: string
  title: string
  description: string
  short_description: string
  tactic_type: 'organic' | 'paid' | 'content' | 'lead' | 'reporting'
  level: 'beginner' | 'intermediate' | 'advanced'
  cost: 'free' | 'low' | 'medium' | 'high'
  channel: 'social_media' | 'email' | 'seo' | 'ppc' | 'content_marketing' | 'influencer' | 'partnerships' | 'direct' | 'other'
  estimated_time_hours: number
  estimated_cost_usd: number
  tools_required: string[]
  steps: string[]
  expected_outcome: string
  tags: string[]
  business_stages: string[]
  success_metrics: string[]
  created_at: string
  is_active: boolean
  usage_count: number
  rating?: number
}

export interface PlanTactic {
  id: string
  plan_id: string
  tactic_id: string
  priority: number
  status: 'pending' | 'in_progress' | 'completed' | 'skipped'
  notes?: string
  created_at: string
  updated_at?: string
  tactic?: Tactic
}

export interface Consultation {
  id: string
  user_id: string
  consultant_id?: string
  consultation_type: 'strategy' | 'implementation' | 'review' | 'general'
  preferred_date: string
  confirmed_date?: string
  duration_minutes: number
  description: string
  specific_topics: string[]
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  created_at: string
  meeting_link?: string
  rating?: number
}

export interface HelpRequest {
  id: string
  user_id: string
  tactic_id: string
  subject: string
  description: string
  priority: number
  status: 'pending' | 'in_progress' | 'resolved' | 'closed'
  created_at: string
  assigned_to?: string
  resolution_notes?: string
}

export interface ChatMessage {
  id: string
  user_id: string
  message: string
  response: string
  context?: Record<string, any>
  created_at: string
}

export interface ApiResponse<T> {
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  per_page: number
  total_pages: number
}

// Form types
export interface QuizFormData {
  business_type: string
  business_stage: string
  growth_speed: string
  main_challenge: string
  time_commitment: string
  budget: string
  business_description: string
  primary_goal: string
}

export interface LoginFormData {
  email: string
  password: string
}

export interface RegisterFormData {
  full_name: string
  email: string
  phone?: string
  password: string
  confirmPassword: string
}

export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

// Filter types
export interface TacticFilters {
  tactic_type?: string
  level?: string
  cost?: string
  channel?: string
  business_stage?: string
  search_query?: string
}

// Subscription types
export interface SubscriptionPlan {
  id: string
  name: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
}

export interface Subscription {
  id: string
  user_id: string
  plan_id: string
  plan_name: string
  status: 'active' | 'cancelled' | 'past_due' | 'unpaid'
  current_period_start: string
  current_period_end: string
  cancel_at_period_end: boolean
  created_at: string
}
