from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, Dict, Any

from app.api.auth import get_current_user
from app.services.ai_service import ai_service
from app.services.supabase_client import get_supabase

router = APIRouter()

class ChatMessage(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = {}

class ChatResponse(BaseModel):
    response: str
    context: Optional[Dict[str, Any]] = {}

class TacticExplanationRequest(BaseModel):
    tactic_id: str
    user_context: Optional[Dict[str, Any]] = {}

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(
    chat_message: ChatMessage,
    current_user: dict = Depends(get_current_user)
):
    """Chat with AI assistant"""
    try:
        # Add user context
        context = chat_message.context or {}
        context["user_id"] = current_user["id"]
        context["user_name"] = current_user["full_name"]
        
        # Get AI response
        response = await ai_service.chat_response(chat_message.message, context)
        
        return ChatResponse(
            response=response,
            context=context
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get AI response: {str(e)}"
        )

@router.post("/explain-tactic", response_model=ChatResponse)
async def explain_tactic(
    request: TacticExplanationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Get AI explanation of a specific tactic"""
    try:
        # Get tactic details (in a real app, this would be from database)
        from app.api.tactics import SAMPLE_TACTICS
        tactic_data = next((t for t in SAMPLE_TACTICS if t["id"] == request.tactic_id), None)
        
        if not tactic_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tactic not found"
            )
        
        # Create tactic response object
        from app.models.tactic import TacticResponse
        tactic = TacticResponse(**tactic_data)
        
        # Get user business context
        user_context = request.user_context or {}
        user_context["user_id"] = current_user["id"]
        user_context["user_name"] = current_user["full_name"]
        
        # Get user's growth plan context if available
        supabase = get_supabase()
        try:
            plans_result = supabase.table('growth_plans').select('business_profile').eq('user_id', current_user["id"]).eq('is_active', True).execute()
            if plans_result.data:
                business_profile = plans_result.data[0]["business_profile"]
                user_context.update(business_profile)
        except:
            pass  # Continue without business context if not available
        
        # Get AI explanation
        explanation = await ai_service.explain_tactic(tactic, user_context)
        
        return ChatResponse(
            response=explanation,
            context={"tactic_id": request.tactic_id, "tactic_title": tactic.title}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to explain tactic: {str(e)}"
        )

@router.get("/suggestions")
async def get_ai_suggestions(current_user: dict = Depends(get_current_user)):
    """Get AI-powered suggestions for the user"""
    try:
        # Get user's growth plans and business context
        supabase = get_supabase()
        
        suggestions = []
        
        try:
            plans_result = supabase.table('growth_plans').select('*').eq('user_id', current_user["id"]).eq('is_active', True).execute()
            
            if plans_result.data:
                plan = plans_result.data[0]
                business_profile = plan["business_profile"]
                
                # Generate contextual suggestions based on business stage
                stage = business_profile.get("business_stage", "launch")
                
                if stage == "idea":
                    suggestions = [
                        "ابدأ ببحث السوق لفهم احتياجات عملائك المحتملين",
                        "قم بإنشاء نموذج أولي (MVP) لاختبار فكرتك",
                        "ابحث عن شركاء أو مستثمرين محتملين"
                    ]
                elif stage == "launch":
                    suggestions = [
                        "ركز على بناء حضور قوي على وسائل التواصل الاجتماعي",
                        "قم بإنشاء محتوى قيم لجذب عملائك المستهدفين",
                        "فكر في عروض الإطلاق لجذب العملاء الأوائل"
                    ]
                elif stage == "growth":
                    suggestions = [
                        "استثمر في الإعلانات المدفوعة لتوسيع نطاق وصولك",
                        "قم بتحليل بيانات عملائك لتحسين استراتيجيتك",
                        "فكر في شراكات استراتيجية لتسريع النمو"
                    ]
                else:
                    suggestions = [
                        "قم بمراجعة خطة النمو الخاصة بك بانتظام",
                        "استخدم البيانات لاتخاذ قرارات مدروسة",
                        "لا تتردد في طلب المساعدة من الخبراء"
                    ]
            else:
                suggestions = [
                    "ابدأ بإنشاء خطة نمو مخصصة لمشروعك",
                    "استكشف مكتبة التكتيكات للعثور على استراتيجيات مناسبة",
                    "احجز استشارة مع خبير للحصول على نصائح شخصية"
                ]
        except:
            suggestions = [
                "مرحباً بك في Growlytics! ابدأ بإنشاء خطة نمو",
                "استكشف مكتبة التكتيكات المتاحة",
                "لا تتردد في استخدام المساعد الذكي للحصول على نصائح"
            ]
        
        return {
            "suggestions": suggestions,
            "user_name": current_user["full_name"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get suggestions: {str(e)}"
        )
