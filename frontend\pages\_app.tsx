import type { AppProps } from 'next/app'
import { QueryClient, QueryClientProvider } from 'react-query'
import { Toaster } from 'react-hot-toast'
import { useState } from 'react'
import Head from 'next/head'

import '@/styles/globals.css'
import { AuthProvider } from '@/contexts/AuthContext'

export default function App({ Component, pageProps }: AppProps) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        retry: 1,
        refetchOnWindowFocus: false,
      },
    },
  }))

  return (
    <>
      <Head>
        <title>Growlytics - أداة توليد خطط النمو للمشاريع الصغيرة</title>
        <meta name="description" content="أداة تفاعلية تساعد أصحاب المشاريع الصغيرة في توليد خطة نمو شخصية" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <meta property="og:title" content="Growlytics - أداة توليد خطط النمو" />
        <meta property="og:description" content="أداة تفاعلية تساعد أصحاب المشاريع الصغيرة في توليد خطة نمو شخصية" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
      </Head>
      
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <div className="min-h-screen bg-gray-50">
            <Component {...pageProps} />
          </div>
          
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
                fontFamily: 'Cairo, system-ui, sans-serif',
                direction: 'rtl',
              },
              success: {
                style: {
                  background: '#10b981',
                },
              },
              error: {
                style: {
                  background: '#ef4444',
                },
              },
            }}
          />
        </AuthProvider>
      </QueryClientProvider>
    </>
  )
}
