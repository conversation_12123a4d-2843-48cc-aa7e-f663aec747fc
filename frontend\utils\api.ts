import axios, { AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token')
      window.location.href = '/auth/login'
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('حدث خطأ في الخادم. يرجى المحاولة لاحقاً')
    } else if (error.code === 'ECONNABORTED') {
      // Timeout
      toast.error('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى')
    }
    
    return Promise.reject(error)
  }
)

// API endpoints
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/api/auth/login', { email, password }),
  
  register: (data: { email: string; password: string; full_name: string; phone?: string }) =>
    api.post('/api/auth/register', data),
  
  me: () => api.get('/api/auth/me'),
  
  logout: () => api.post('/api/auth/logout'),
  
  forgotPassword: (email: string) =>
    api.post('/api/auth/forgot-password', { email }),
  
  resetPassword: (token: string, new_password: string) =>
    api.post('/api/auth/reset-password', { token, new_password }),
}

export const growthPlansAPI = {
  generateSummary: (quizAnswers: any) =>
    api.post('/api/growth-plans/generate-summary', quizAnswers),
  
  create: (quizAnswers: any) =>
    api.post('/api/growth-plans/create', quizAnswers),
  
  getAll: () => api.get('/api/growth-plans/'),
  
  getById: (id: string) => api.get(`/api/growth-plans/${id}`),
  
  update: (id: string, data: any) =>
    api.put(`/api/growth-plans/${id}`, data),
  
  delete: (id: string) => api.delete(`/api/growth-plans/${id}`),
}

export const tacticsAPI = {
  getAll: (filters?: any) => {
    const params = new URLSearchParams()
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          params.append(key, filters[key])
        }
      })
    }
    return api.get(`/api/tactics/?${params.toString()}`)
  },
  
  getById: (id: string) => api.get(`/api/tactics/${id}`),
  
  addToPlan: (planId: string, tacticId: string, priority: number = 1) =>
    api.post('/api/tactics/add-to-plan', {
      plan_id: planId,
      tactic_id: tacticId,
      priority,
    }),
  
  getPlanTactics: (planId: string) =>
    api.get(`/api/tactics/plan/${planId}`),
}

export const consultationsAPI = {
  book: (data: any) => api.post('/api/consultations/book', data),
  
  getAll: () => api.get('/api/consultations/'),
  
  createHelpRequest: (data: any) =>
    api.post('/api/consultations/help-request', data),
  
  getHelpRequests: () => api.get('/api/consultations/help-requests'),
}

export const aiAPI = {
  chat: (message: string, context?: any) =>
    api.post('/api/ai/chat', { message, context }),
  
  explainTactic: (tacticId: string, userContext?: any) =>
    api.post('/api/ai/explain-tactic', {
      tactic_id: tacticId,
      user_context: userContext,
    }),
  
  getSuggestions: () => api.get('/api/ai/suggestions'),
}

export const paymentsAPI = {
  getPlans: () => api.get('/api/payments/plans'),
  
  createPaymentIntent: (amount: number, description?: string) =>
    api.post('/api/payments/create-payment-intent', {
      amount,
      description,
      currency: 'usd',
    }),
  
  createSubscription: (planId: string) =>
    api.post('/api/payments/create-subscription', { plan_id: planId }),
  
  getSubscription: () => api.get('/api/payments/subscription'),
  
  cancelSubscription: () => api.post('/api/payments/cancel-subscription'),
}

export const usersAPI = {
  getProfile: () => api.get('/api/users/profile'),
  
  updateProfile: (data: any) => api.put('/api/users/profile', data),
  
  deleteAccount: () => api.delete('/api/users/account'),
}

export default api
