import { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { motion } from 'framer-motion'
import { 
  CalendarIcon,
  ClockIcon,
  PlayIcon,
  PauseIcon,
  CheckIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'
import { useAuth } from '@/hooks/useAuth'

interface TimelineTask {
  id: string
  title: string
  description: string
  duration: number // in hours
  dependencies: string[] // task IDs that must be completed first
  canRunParallel: boolean
  priority: 'عالية' | 'متوسطة' | 'منخفضة'
  category: 'تحضير' | 'تنفيذ' | 'متابعة' | 'تحليل'
  estimatedResults: string[]
  requiredTools: string[]
  difficulty: 'سهل' | 'متوسط' | 'صعب'
}

interface WeekPlan {
  weekNumber: number
  weekTitle: string
  totalHours: number
  tasks: TimelineTask[]
  parallelTasks: TimelineTask[][]
  weeklyGoal: string
}

export default function ProfessionalTimeline() {
  const [weekPlans, setWeekPlans] = useState<WeekPlan[]>([])
  const [selectedWeek, setSelectedWeek] = useState<number>(1)
  const [timeCommitment, setTimeCommitment] = useState<number>(20) // hours per week
  const [showParallelOptions, setShowParallelOptions] = useState<boolean>(true)
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    const storedAnswers = localStorage.getItem('quizAnswers')
    if (storedAnswers) {
      try {
        const answers = JSON.parse(storedAnswers)
        const commitment = getTimeCommitmentHours(answers.time_commitment)
        setTimeCommitment(commitment)
        generateProfessionalTimeline(answers, commitment)
      } catch (error) {
        console.error('Error parsing quiz answers:', error)
        router.push('/dashboard')
      }
    } else {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const getTimeCommitmentHours = (commitment: string): number => {
    const mapping: { [key: string]: number } = {
      'less_than_2': 8,
      '2_to_5': 20,
      '5_to_10': 35,
      'full_time': 60
    }
    return mapping[commitment] || 20
  }

  const generateProfessionalTimeline = (quizAnswers: any, weeklyHours: number) => {
    const businessType = quizAnswers?.business_type || 'مشروعك'
    const budget = quizAnswers?.budget || 'محدودة'
    
    // Define all tasks
    const allTasks: TimelineTask[] = [
      {
        id: 'market-research',
        title: `بحث السوق لـ ${businessType}`,
        description: `دراسة شاملة للسوق المستهدف وتحليل المنافسين في مجال ${businessType}`,
        duration: 8,
        dependencies: [],
        canRunParallel: false,
        priority: 'عالية',
        category: 'تحضير',
        estimatedResults: ['فهم عميق للسوق', 'تحديد الفرص', 'معرفة نقاط القوة والضعف'],
        requiredTools: ['Google Trends', 'استطلاعات رأي', 'تحليل المنافسين'],
        difficulty: 'متوسط'
      },
      {
        id: 'brand-identity',
        title: 'تطوير الهوية البصرية',
        description: `إنشاء شعار وألوان وخطوط مميزة لـ ${businessType}`,
        duration: 6,
        dependencies: ['market-research'],
        canRunParallel: true,
        priority: 'عالية',
        category: 'تحضير',
        estimatedResults: ['هوية بصرية متميزة', 'شعار احترافي', 'دليل الهوية البصرية'],
        requiredTools: ['Canva Pro', 'Adobe Illustrator', 'مولد الألوان'],
        difficulty: 'متوسط'
      },
      {
        id: 'website-creation',
        title: 'إنشاء الموقع الإلكتروني',
        description: `تصميم وتطوير موقع احترافي لـ ${businessType}`,
        duration: 12,
        dependencies: ['brand-identity'],
        canRunParallel: false,
        priority: 'عالية',
        category: 'تنفيذ',
        estimatedResults: ['موقع احترافي', 'صفحات هبوط فعالة', 'تجربة مستخدم ممتازة'],
        requiredTools: ['WordPress', 'Elementor', 'استضافة ويب'],
        difficulty: 'صعب'
      },
      {
        id: 'content-strategy',
        title: 'استراتيجية المحتوى',
        description: `وضع خطة محتوى شاملة لجذب عملاء ${businessType}`,
        duration: 4,
        dependencies: ['market-research'],
        canRunParallel: true,
        priority: 'عالية',
        category: 'تحضير',
        estimatedResults: ['خطة محتوى شهرية', 'تقويم النشر', 'أفكار محتوى متنوعة'],
        requiredTools: ['تقويم المحتوى', 'أدوات البحث', 'قوالب المحتوى'],
        difficulty: 'سهل'
      },
      {
        id: 'content-creation',
        title: 'إنتاج المحتوى',
        description: 'إنشاء محتوى قيم ومتنوع للشهر الأول',
        duration: 10,
        dependencies: ['content-strategy', 'brand-identity'],
        canRunParallel: true,
        priority: 'متوسطة',
        category: 'تنفيذ',
        estimatedResults: ['20 منشور جاهز', 'محتوى مرئي جذاب', 'مكتبة محتوى'],
        requiredTools: ['Canva', 'كاميرا/هاتف', 'أدوات التحرير'],
        difficulty: 'متوسط'
      },
      {
        id: 'social-media-setup',
        title: 'إعداد منصات التواصل',
        description: 'إنشاء وتحسين حسابات وسائل التواصل الاجتماعي',
        duration: 3,
        dependencies: ['brand-identity'],
        canRunParallel: true,
        priority: 'متوسطة',
        category: 'تنفيذ',
        estimatedResults: ['حسابات احترافية', 'صور بروفايل وكوفر', 'وصف محسن'],
        requiredTools: ['Facebook Business', 'Instagram', 'LinkedIn'],
        difficulty: 'سهل'
      },
      {
        id: 'launch-campaign',
        title: `حملة إطلاق ${businessType}`,
        description: 'تنفيذ حملة إطلاق شاملة ومنسقة',
        duration: 8,
        dependencies: ['website-creation', 'content-creation', 'social-media-setup'],
        canRunParallel: false,
        priority: 'عالية',
        category: 'تنفيذ',
        estimatedResults: ['وعي بالعلامة التجارية', 'زوار للموقع', 'عملاء محتملين'],
        requiredTools: ['أدوات الجدولة', 'إعلانات مدفوعة', 'أدوات التحليل'],
        difficulty: 'متوسط'
      },
      {
        id: 'performance-analysis',
        title: 'تحليل الأداء والتحسين',
        description: 'قياس نتائج الحملة وتحسين الاستراتيجية',
        duration: 4,
        dependencies: ['launch-campaign'],
        canRunParallel: false,
        priority: 'متوسطة',
        category: 'تحليل',
        estimatedResults: ['تقرير أداء شامل', 'نقاط التحسين', 'خطة التطوير'],
        requiredTools: ['Google Analytics', 'إحصائيات وسائل التواصل', 'أدوات التقييم'],
        difficulty: 'متوسط'
      }
    ]

    // Generate optimized weekly plans
    const plans = generateOptimizedWeeklyPlans(allTasks, weeklyHours)
    setWeekPlans(plans)
  }

  const generateOptimizedWeeklyPlans = (tasks: TimelineTask[], weeklyHours: number): WeekPlan[] => {
    const plans: WeekPlan[] = []
    let remainingTasks = [...tasks]
    let weekNumber = 1

    while (remainingTasks.length > 0) {
      const weekTasks: TimelineTask[] = []
      const parallelGroups: TimelineTask[][] = []
      let weekHours = 0

      // Find tasks that can be started this week
      const availableTasks = remainingTasks.filter(task => 
        task.dependencies.every(dep => 
          !remainingTasks.some(remaining => remaining.id === dep)
        )
      )

      // Sort by priority and duration
      availableTasks.sort((a, b) => {
        const priorityOrder = { 'عالية': 3, 'متوسطة': 2, 'منخفضة': 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })

      // Add tasks to week
      for (const task of availableTasks) {
        if (weekHours + task.duration <= weeklyHours) {
          weekTasks.push(task)
          weekHours += task.duration
          remainingTasks = remainingTasks.filter(t => t.id !== task.id)
        }
      }

      // Group parallel tasks
      const parallelTasks = weekTasks.filter(t => t.canRunParallel)
      const sequentialTasks = weekTasks.filter(t => !t.canRunParallel)

      if (parallelTasks.length > 1) {
        parallelGroups.push(parallelTasks)
      }

      plans.push({
        weekNumber,
        weekTitle: getWeekTitle(weekNumber),
        totalHours: weekHours,
        tasks: weekTasks,
        parallelTasks: parallelGroups,
        weeklyGoal: getWeeklyGoal(weekNumber, weekTasks)
      })

      weekNumber++
      
      // Safety break
      if (weekNumber > 8) break
    }

    return plans
  }

  const getWeekTitle = (weekNumber: number): string => {
    const titles = [
      'أسبوع التحضير والبحث',
      'أسبوع بناء الهوية',
      'أسبوع التطوير والإنشاء',
      'أسبوع الإنتاج والتحضير',
      'أسبوع الإطلاق',
      'أسبوع المتابعة والتحليل',
      'أسبوع التحسين والتطوير',
      'أسبوع التوسع والنمو'
    ]
    return titles[weekNumber - 1] || `الأسبوع ${weekNumber}`
  }

  const getWeeklyGoal = (weekNumber: number, tasks: TimelineTask[]): string => {
    if (tasks.some(t => t.category === 'تحضير')) {
      return 'إنهاء مرحلة التحضير والتخطيط'
    } else if (tasks.some(t => t.category === 'تنفيذ')) {
      return 'تنفيذ المهام الأساسية للمشروع'
    } else if (tasks.some(t => t.category === 'متابعة')) {
      return 'متابعة النتائج والتفاعل مع العملاء'
    } else {
      return 'تحليل الأداء والتحسين المستمر'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالية': return 'text-red-600 bg-red-100 border-red-200'
      case 'متوسطة': return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      case 'منخفضة': return 'text-green-600 bg-green-100 border-green-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'تحضير': return 'text-blue-600 bg-blue-100'
      case 'تنفيذ': return 'text-purple-600 bg-purple-100'
      case 'متابعة': return 'text-orange-600 bg-orange-100'
      case 'تحليل': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري إنشاء الجدول الزمني الاحترافي...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <>
      <Head>
        <title>الجدول الزمني الاحترافي - Growlytics</title>
        <meta name="description" content="جدول زمني احترافي مع إمكانية التنفيذ المتوازي والتحكم في الوقت" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/dashboard">
                <Button variant="outline" className="mb-4">
                  <ArrowLeftIcon className="w-4 h-4 ml-2" />
                  العودة للوحة التحكم
                </Button>
              </Link>
              
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    📅 الجدول الزمني الاحترافي
                  </h1>
                  <p className="text-gray-600">
                    خطة تنفيذ محترفة مع إمكانية التنفيذ المتوازي وإدارة الوقت الذكية
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500">الوقت المتاح أسبوعياً</div>
                  <div className="text-2xl font-bold text-primary-600">{timeCommitment} ساعة</div>
                </div>
              </div>
            </motion.div>

            {/* Timeline Overview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="card mb-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900 flex items-center">
                  <ChartBarIcon className="w-5 h-5 ml-2 text-primary-600" />
                  نظرة عامة على الجدول الزمني
                </h2>
                <div className="flex items-center gap-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showParallelOptions}
                      onChange={(e) => setShowParallelOptions(e.target.checked)}
                      className="ml-2"
                    />
                    <span className="text-sm text-gray-600">إظهار خيارات التنفيذ المتوازي</span>
                  </label>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {weekPlans.map((week, index) => (
                  <div
                    key={week.weekNumber}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedWeek === week.weekNumber
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedWeek(week.weekNumber)}
                  >
                    <div className="text-sm font-medium text-primary-600 mb-1">
                      الأسبوع {week.weekNumber}
                    </div>
                    <div className="font-semibold text-gray-900 mb-2 text-sm">
                      {week.weekTitle}
                    </div>
                    <div className="text-xs text-gray-600 mb-2">
                      {week.tasks.length} مهام • {week.totalHours} ساعة
                    </div>
                    <div className="text-xs text-gray-500">
                      {week.weeklyGoal}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Selected Week Details */}
            {weekPlans.length > 0 && (
              <motion.div
                key={selectedWeek}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="card"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">
                    {weekPlans[selectedWeek - 1]?.weekTitle}
                  </h3>
                  <div className="flex items-center gap-4">
                    <div className="text-sm text-gray-500">
                      إجمالي الوقت: <span className="font-medium">{weekPlans[selectedWeek - 1]?.totalHours} ساعة</span>
                    </div>
                  </div>
                </div>

                {/* Parallel Tasks Warning */}
                {showParallelOptions && weekPlans[selectedWeek - 1]?.parallelTasks.length > 0 && (
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <InformationCircleIcon className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-900 mb-1">إمكانية التنفيذ المتوازي</h4>
                        <p className="text-blue-700 text-sm">
                          يمكنك تنفيذ بعض المهام في نفس الوقت لتوفير الوقت. المهام التالية يمكن تنفيذها بالتوازي:
                        </p>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {weekPlans[selectedWeek - 1]?.parallelTasks[0]?.map((task, index) => (
                            <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                              {task.title}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Tasks List */}
                <div className="space-y-4">
                  {weekPlans[selectedWeek - 1]?.tasks.map((task, index) => (
                    <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-900">{task.title}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                              {task.priority}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(task.category)}`}>
                              {task.category}
                            </span>
                            {task.canRunParallel && (
                              <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                                متوازي
                              </span>
                            )}
                          </div>
                          <p className="text-gray-600 text-sm mb-3">{task.description}</p>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                            <span className="flex items-center">
                              <ClockIcon className="w-4 h-4 ml-1" />
                              {task.duration} ساعة
                            </span>
                            <span className="flex items-center">
                              <span className={`w-2 h-2 rounded-full ml-1 ${
                                task.difficulty === 'سهل' ? 'bg-green-500' :
                                task.difficulty === 'متوسط' ? 'bg-yellow-500' : 'bg-red-500'
                              }`}></span>
                              {task.difficulty}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h5 className="font-medium text-gray-900 mb-2 text-sm">الأدوات المطلوبة:</h5>
                              <div className="flex flex-wrap gap-1">
                                {task.requiredTools.map((tool, toolIndex) => (
                                  <span key={toolIndex} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                    {tool}
                                  </span>
                                ))}
                              </div>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900 mb-2 text-sm">النتائج المتوقعة:</h5>
                              <div className="space-y-1">
                                {task.estimatedResults.slice(0, 2).map((result, resultIndex) => (
                                  <div key={resultIndex} className="flex items-center gap-2">
                                    <CheckIcon className="w-3 h-3 text-green-600 flex-shrink-0" />
                                    <span className="text-gray-600 text-xs">{result}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <Link href={`/task-details/${task.id}`}>
                          <Button size="sm" variant="outline">
                            التفاصيل
                            <ArrowRightIcon className="w-3 h-3 mr-1" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Week Navigation */}
                <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
                  <Button
                    variant="outline"
                    disabled={selectedWeek === 1}
                    onClick={() => setSelectedWeek(selectedWeek - 1)}
                  >
                    <ArrowLeftIcon className="w-4 h-4 ml-2" />
                    الأسبوع السابق
                  </Button>
                  
                  <div className="text-sm text-gray-500">
                    الأسبوع {selectedWeek} من {weekPlans.length}
                  </div>
                  
                  <Button
                    variant="outline"
                    disabled={selectedWeek === weekPlans.length}
                    onClick={() => setSelectedWeek(selectedWeek + 1)}
                  >
                    الأسبوع التالي
                    <ArrowRightIcon className="w-4 h-4 mr-2" />
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </Layout>
    </>
  )
}
