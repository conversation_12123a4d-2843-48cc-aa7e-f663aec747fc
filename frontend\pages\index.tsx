import { useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  ChartBarIcon, 
  LightBulbIcon, 
  UserGroupIcon,
  ArrowRightIcon,
  CheckIcon,
  StarIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'

const features = [
  {
    icon: ChartBarIcon,
    title: 'خطة نمو مخصصة',
    description: 'احصل على خطة نمو مصممة خصيصاً لمشروعك ومرحلته الحالية'
  },
  {
    icon: LightBulbIcon,
    title: 'مكتبة تكتيكات شاملة',
    description: 'استكشف مئات التكتيكات التسويقية المجربة والمختبرة'
  },
  {
    icon: UserGroupIcon,
    title: 'دعم من الخبراء',
    description: 'احصل على مساعدة من خبراء النمو في تنفيذ استراتيجيتك'
  }
]

const steps = [
  {
    number: '01',
    title: 'أجب على الأسئلة',
    description: 'أجب على مجموعة بسيطة من الأسئلة حول مشروعك وأهدافك'
  },
  {
    number: '02',
    title: 'احصل على خطتك',
    description: 'سيقوم الذكاء الاصطناعي بتوليد خطة نمو مخصصة لك'
  },
  {
    number: '03',
    title: 'نفذ واحصل على النتائج',
    description: 'ابدأ تنفيذ التكتيكات واحصل على الدعم عند الحاجة'
  }
]

const testimonials = [
  {
    name: 'أحمد محمد',
    role: 'مؤسس متجر إلكتروني',
    content: 'ساعدتني Growlytics في زيادة مبيعاتي بنسبة 150% خلال 3 أشهر فقط!',
    rating: 5
  },
  {
    name: 'فاطمة علي',
    role: 'صاحبة مطعم',
    content: 'الخطة كانت واضحة وعملية، والدعم من الفريق كان ممتاز',
    rating: 5
  },
  {
    name: 'محمد حسن',
    role: 'مستشار تقني',
    content: 'أفضل أداة استخدمتها لتطوير استراتيجية التسويق لشركتي',
    rating: 5
  }
]

export default function HomePage() {
  return (
    <>
      <Head>
        <title>Growlytics - أداة توليد خطط النمو للمشاريع الصغيرة</title>
        <meta name="description" content="أداة تفاعلية تساعد أصحاب المشاريع الصغيرة في توليد خطة نمو شخصية بناءً على مرحلة مشروعهم والميزانية والأهداف" />
      </Head>

      <Layout>
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="text-center">
              <motion.h1 
                className="text-4xl md:text-6xl font-bold text-gray-900 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                اصنع خطة نمو{' '}
                <span className="text-gradient">مخصصة</span>{' '}
                لمشروعك
              </motion.h1>
              
              <motion.p 
                className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                أداة تفاعلية تساعد أصحاب المشاريع الصغيرة في توليد خطة نمو شخصية 
                بناءً على مرحلة مشروعهم، الميزانية، والأهداف
              </motion.p>
              
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 justify-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Link href="/quiz">
                  <Button size="lg" className="group">
                    ابدأ الآن مجاناً
                    <ArrowRightIcon className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                
                <Link href="/library">
                  <Button variant="outline" size="lg">
                    استكشف المكتبة
                  </Button>
                </Link>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                لماذا تختار Growlytics؟
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                نقدم لك الأدوات والخبرة اللازمة لنمو مشروعك بطريقة مدروسة وفعالة
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  className="card text-center"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                >
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="w-8 h-8 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* How it Works Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                كيف يعمل النظام؟
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                ثلاث خطوات بسيطة للحصول على خطة نمو مخصصة لمشروعك
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {steps.map((step, index) => (
                <motion.div
                  key={step.number}
                  className="text-center relative"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                >
                  <div className="w-20 h-20 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                    {step.number}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600">
                    {step.description}
                  </p>
                  
                  {index < steps.length - 1 && (
                    <div className="hidden md:block absolute top-10 left-full w-full">
                      <ArrowRightIcon className="w-6 h-6 text-gray-400 mx-auto" />
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                ماذا يقول عملاؤنا؟
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                آراء حقيقية من رواد أعمال حققوا نجاحات مميزة باستخدام Growlytics
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.name}
                  className="card"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                >
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-6 italic">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {testimonial.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {testimonial.role}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 gradient-bg">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                جاهز لبدء رحلة النمو؟
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                انضم إلى آلاف رواد الأعمال الذين يستخدمون Growlytics لتنمية مشاريعهم
              </p>
              
              <Link href="/quiz">
                <Button size="lg" variant="secondary" className="group">
                  ابدأ خطتك المجانية الآن
                  <ArrowRightIcon className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </section>
      </Layout>
    </>
  )
}
