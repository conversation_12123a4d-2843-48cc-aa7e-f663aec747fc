# Growlytics - أداة توليد خطط النمو للمشاريع الصغيرة

## 🎯 نظرة عامة
أداة تفاعلية عبر الإنترنت تساعد أصحاب المشاريع الصغيرة في توليد خطة نمو شخصية بناءً على مرحلة مشروعهم، الميزانية، والأهداف.

## 🏗️ البنية التقنية
- **Backend**: FastAPI + Supabase
- **Frontend**: Next.js + TailwindCSS + TypeScript
- **AI**: OpenAI API
- **Payment**: Stripe API
- **Auth**: Supabase Auth

## 🚀 التشغيل السريع

### متطلبات النظام
- Python 3.8+
- Node.js 16+
- npm أو yarn
- Docker (اختياري)

### إعداد متغيرات البيئة
1. انسخ ملف `.env.example` إلى `.env`
2. املأ المتغيرات المطلوبة:
   - Supabase URL و Keys
   - OpenAI API Key
   - Stripe Keys
   - JWT Secret

### التشغيل باستخدام Docker (الطريقة المفضلة)
```bash
# انسخ متغيرات البيئة
cp .env.example .env

# شغل المشروع
docker-compose up -d

# الوصول للتطبيق
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### التشغيل اليدوي

#### إعداد Backend
```bash
cd backend
pip install -r requirements.txt
cp ../.env.example .env
# املأ متغيرات البيئة في ملف .env
uvicorn main:app --reload
```

#### إعداد Frontend
```bash
cd frontend
npm install
cp ../.env.example .env.local
# املأ متغيرات البيئة في ملف .env.local
npm run dev
```

## 📁 هيكل المشروع
```
Growlytics/
├── backend/                 # FastAPI Backend
├── frontend/               # Next.js Frontend
├── shared/                # Shared configurations
└── README.md
```

## 🌟 الميزات الرئيسية
- ✅ توليد خطة نمو مخصصة باستخدام AI
- ✅ مكتبة تكتيكات تسويقية شاملة (10+ تكتيك)
- ✅ طلب المساعدة في التنفيذ
- ✅ حجز استشارات مع خبراء
- ✅ AI Chatbot مدمج للمساعدة
- ✅ نظام مصادقة كامل
- ✅ لوحة تحكم تفاعلية
- ✅ دعم الدفع عبر Stripe
- ✅ تصميم متجاوب (Mobile-First)
- ✅ دعم اللغة العربية

## 🗂️ بنية المشروع
```
Growlytics/
├── backend/                 # FastAPI Backend
│   ├── app/
│   │   ├── api/            # API Routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Pydantic models
│   │   └── services/       # Business logic
│   ├── requirements.txt
│   └── main.py
├── frontend/               # Next.js Frontend
│   ├── components/         # React components
│   ├── pages/             # Next.js pages
│   ├── styles/            # TailwindCSS styles
│   ├── utils/             # Utility functions
│   ├── types/             # TypeScript types
│   └── package.json
├── database/              # Database schema & seeds
│   ├── schema.sql         # Supabase schema
│   └── seed_tactics.sql   # Sample tactics data
├── docker-compose.yml     # Development environment
├── start.sh              # Quick start script (Linux/Mac)
├── start.bat             # Quick start script (Windows)
└── README.md
```

## 🔧 التقنيات المستخدمة

### Backend
- **FastAPI** - Python web framework
- **Supabase** - Database & Authentication
- **OpenAI API** - AI-powered features
- **Stripe** - Payment processing
- **Pydantic** - Data validation
- **JWT** - Authentication tokens

### Frontend
- **Next.js 14** - React framework
- **TypeScript** - Type safety
- **TailwindCSS** - Styling
- **Framer Motion** - Animations
- **React Query** - Data fetching
- **React Hook Form** - Form handling
- **Axios** - HTTP client

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Multi-container setup
- **Vercel** - Frontend deployment
- **Render** - Backend deployment

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- `users` - معلومات المستخدمين
- `growth_plans` - خطط النمو
- `tactics` - مكتبة التكتيكات
- `plan_tactics` - ربط الخطط بالتكتيكات
- `consultations` - حجوزات الاستشارات
- `help_requests` - طلبات المساعدة
- `subscriptions` - الاشتراكات
- `chat_messages` - رسائل المحادثة

## 🚀 نشر المشروع

### Frontend (Vercel)
1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Vercel
3. اضبط متغيرات البيئة
4. انشر المشروع

### Backend (Render)
1. اربط المستودع بـ Render
2. اختر خدمة Web Service
3. اضبط متغيرات البيئة
4. انشر المشروع

## 🔐 الأمان
- Row Level Security (RLS) في Supabase
- JWT tokens للمصادقة
- تشفير كلمات المرور
- CORS protection
- Rate limiting
- Input validation

## 🧪 الاختبار
```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

## 📈 المراقبة والتحليلات
- Google Analytics
- Supabase Analytics
- Error tracking
- Performance monitoring

## 🤝 المساهمة
1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## 📞 الدعم
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 Discord: [رابط الخادم]
- 📖 الوثائق: [رابط الوثائق]

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير
- [OpenAI](https://openai.com) للذكاء الاصطناعي
- [Supabase](https://supabase.com) لقاعدة البيانات
- [Vercel](https://vercel.com) للاستضافة
- [TailwindCSS](https://tailwindcss.com) للتصميم

---

صنع بـ ❤️ لرواد الأعمال العرب
