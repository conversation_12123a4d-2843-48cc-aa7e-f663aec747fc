@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Arabic font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', 'Cairo', system-ui, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Arabic text support */
.arabic {
  font-family: 'Cairo', system-ui, sans-serif;
  direction: rtl;
  text-align: right;
}

.english {
  font-family: 'Inter', system-ui, sans-serif;
  direction: ltr;
  text-align: left;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }
  
  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }
  
  .card-hover {
    @apply card hover:shadow-lg transition-shadow duration-200;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-br from-primary-500 via-primary-600 to-secondary-600;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Quiz styles */
.quiz-container {
  @apply max-w-2xl mx-auto;
}

.quiz-question {
  @apply card mb-6 animate-slide-up;
}

.quiz-option {
  @apply p-4 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50;
}

.quiz-option.selected {
  @apply border-primary-600 bg-primary-100 text-primary-800;
}

/* Dashboard styles */
.dashboard-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.stat-card {
  @apply card text-center;
}

.stat-number {
  @apply text-3xl font-bold text-primary-600;
}

.stat-label {
  @apply text-gray-600 mt-2;
}

/* Tactic card styles */
.tactic-card {
  @apply card-hover relative overflow-hidden;
}

.tactic-badge {
  @apply inline-block px-2 py-1 text-xs font-medium rounded-full;
}

.tactic-badge.organic {
  @apply bg-green-100 text-green-800;
}

.tactic-badge.paid {
  @apply bg-blue-100 text-blue-800;
}

.tactic-badge.content {
  @apply bg-purple-100 text-purple-800;
}

.tactic-badge.lead {
  @apply bg-orange-100 text-orange-800;
}

.tactic-badge.reporting {
  @apply bg-gray-100 text-gray-800;
}

/* Chat styles */
.chat-container {
  @apply flex flex-col h-96 border border-gray-200 rounded-lg overflow-hidden;
}

.chat-messages {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
}

.chat-message {
  @apply max-w-xs rounded-lg p-3;
}

.chat-message.user {
  @apply bg-primary-600 text-white ml-auto;
}

.chat-message.ai {
  @apply bg-gray-100 text-gray-800 mr-auto;
}

.chat-input {
  @apply border-t border-gray-200 p-4;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
