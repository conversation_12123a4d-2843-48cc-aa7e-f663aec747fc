{"name": "growlytics-frontend", "version": "1.0.0", "description": "Frontend for Growlytics - Growth Plan Generator", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next/swc-win32-x64-msvc": "^15.3.3", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.2", "@supabase/supabase-js": "^2.38.5", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "module-alias": "^2.2.3", "next": "^14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-pdf": "^9.2.1", "react-query": "^3.39.3", "recharts": "^2.8.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "_moduleAliases": {"@/contexts/authContext": "./contexts/authContext.ts"}}