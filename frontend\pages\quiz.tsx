import { useState } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowRightIcon, 
  ArrowLeftIcon,
  CheckIcon 
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'

interface QuizData {
  business_type: string
  business_stage: string
  growth_speed: string
  main_challenge: string
  time_commitment: string
  budget: string
  business_description: string
  primary_goal: string
}

const questions = [
  {
    id: 'business_type',
    title: 'ما نوع نشاطك التجاري؟',
    type: 'input',
    placeholder: 'مثال: متجر إلكتروني، مطعم، خدمات استشارية...'
  },
  {
    id: 'business_stage',
    title: 'في أي مرحلة أنت؟',
    type: 'select',
    options: [
      { value: 'idea', label: 'فكرة - لا يزال في مرحلة التخطيط' },
      { value: 'launch', label: 'إطلاق - بدأت للتو أو على وشك البدء' },
      { value: 'profit', label: 'ربح - أحقق أرباحاً ولكن أريد المزيد' },
      { value: 'growth', label: 'نمو - أريد توسيع نطاق العمل' },
      { value: 'scale', label: 'جاهز للتوسع - أريد دخول أسواق جديدة' }
    ]
  },
  {
    id: 'growth_speed',
    title: 'مدى سرعة النمو المرغوبة؟',
    type: 'select',
    options: [
      { value: 'slow', label: 'بطيء ومستقر - أفضل النمو التدريجي' },
      { value: 'medium', label: 'متوسط - نمو معتدل ومدروس' },
      { value: 'fast', label: 'سريع - أريد نتائج سريعة' }
    ]
  },
  {
    id: 'main_challenge',
    title: 'ما التحدي الأكبر حالياً؟',
    type: 'select',
    options: [
      { value: 'traffic', label: 'الزوار - أحتاج المزيد من العملاء المحتملين' },
      { value: 'conversion', label: 'التحويل - الزوار لا يشترون' },
      { value: 'retention', label: 'الاحتفاظ - العملاء لا يعودون' },
      { value: 'content', label: 'المحتوى - لا أعرف ماذا أنشر' },
      { value: 'unknown', label: 'مش عارف - محتاج مساعدة في التشخيص' }
    ]
  },
  {
    id: 'time_commitment',
    title: 'وقتك الأسبوعي للتسويق؟',
    type: 'select',
    options: [
      { value: 'less_than_2', label: 'أقل من ساعتين - وقت محدود جداً' },
      { value: '2_to_5', label: '2-5 ساعات - وقت معقول' },
      { value: '5_to_10', label: '5-10 ساعات - وقت جيد' },
      { value: 'full_time', label: 'دوام كامل - التسويق هو تركيزي الأساسي' }
    ]
  },
  {
    id: 'budget',
    title: 'ميزانية التسويق شهرياً؟',
    type: 'select',
    options: [
      { value: 'less_than_100', label: 'أقل من 100$ - ميزانية محدودة' },
      { value: '100_to_500', label: '100-500$ - ميزانية متوسطة' },
      { value: '500_to_1000', label: '500-1000$ - ميزانية جيدة' },
      { value: 'more_than_1000', label: 'أكثر من 1000$ - ميزانية مرنة' }
    ]
  },
  {
    id: 'business_description',
    title: 'وصف مختصر للنشاط',
    type: 'textarea',
    placeholder: 'اكتب وصفاً مختصراً عن نشاطك التجاري، منتجاتك أو خدماتك، والجمهور المستهدف...'
  },
  {
    id: 'primary_goal',
    title: 'ما هدفك الأساسي؟',
    type: 'textarea',
    placeholder: 'مثال: زيادة المبيعات بنسبة 50%، الوصول لـ 1000 عميل جديد، فتح فرع جديد...'
  }
]

export default function QuizPage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [answers, setAnswers] = useState<Partial<QuizData>>({})
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const currentQuestion = questions[currentStep]
  const isLastStep = currentStep === questions.length - 1
  const canProceed = answers[currentQuestion.id as keyof QuizData]

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }))
  }

  const handleNext = () => {
    if (canProceed) {
      if (isLastStep) {
        handleSubmit()
      } else {
        setCurrentStep(prev => prev + 1)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleSubmit = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/growth-plans/generate-summary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(answers),
        signal: AbortSignal.timeout(60000) // 60 seconds timeout
      })

      if (!response.ok) {
        throw new Error('فشل في توليد الخطة')
      }

      const planSummary = await response.json()
      
      // Store the plan summary and quiz answers in localStorage
      localStorage.setItem('planSummary', JSON.stringify(planSummary))
      localStorage.setItem('quizAnswers', JSON.stringify(answers))
      
      // Redirect to summary page
      router.push('/plan-summary')
    } catch (error: any) {
      toast.error(error.message || 'حدث خطأ في توليد الخطة')
    } finally {
      setLoading(false)
    }
  }

  const progress = ((currentStep + 1) / questions.length) * 100

  return (
    <>
      <Head>
        <title>استبيان خطة النمو - Growlytics</title>
        <meta name="description" content="أجب على الأسئلة للحصول على خطة نمو مخصصة لمشروعك" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-12">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>السؤال {currentStep + 1} من {questions.length}</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>

            {/* Question */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="quiz-question"
              >
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8">
                  {currentQuestion.title}
                </h2>

                {currentQuestion.type === 'select' && (
                  <div className="space-y-3">
                    {currentQuestion.options?.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleAnswer(option.value)}
                        className={`quiz-option w-full text-right ${
                          answers[currentQuestion.id as keyof QuizData] === option.value
                            ? 'selected'
                            : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span>{option.label}</span>
                          {answers[currentQuestion.id as keyof QuizData] === option.value && (
                            <CheckIcon className="w-5 h-5 text-primary-600" />
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {currentQuestion.type === 'input' && (
                  <input
                    type="text"
                    placeholder={currentQuestion.placeholder}
                    value={answers[currentQuestion.id as keyof QuizData] || ''}
                    onChange={(e) => handleAnswer(e.target.value)}
                    className="input-field w-full text-lg py-4"
                    autoFocus
                  />
                )}

                {currentQuestion.type === 'textarea' && (
                  <textarea
                    placeholder={currentQuestion.placeholder}
                    value={answers[currentQuestion.id as keyof QuizData] || ''}
                    onChange={(e) => handleAnswer(e.target.value)}
                    rows={4}
                    className="input-field w-full text-lg py-4 resize-none"
                    autoFocus
                  />
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className="flex items-center"
              >
                <ArrowLeftIcon className="w-4 h-4 ml-2" />
                السابق
              </Button>

              <Button
                onClick={handleNext}
                disabled={!canProceed}
                loading={loading}
                className="flex items-center"
              >
                {isLastStep ? 'إنشاء الخطة' : 'التالي'}
                {!isLastStep && <ArrowRightIcon className="w-4 h-4 mr-2" />}
              </Button>
            </div>
          </div>
        </div>
      </Layout>
    </>
  )
}
