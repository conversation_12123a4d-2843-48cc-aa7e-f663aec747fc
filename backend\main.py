from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
from typing import List
from enum import Enum

# Simple Models
class BusinessStage(str, Enum):
    IDEA = "idea"
    LAUNCH = "launch"
    PROFIT = "profit"
    GROWTH = "growth"
    SCALE = "scale"

class GrowthSpeed(str, Enum):
    SLOW = "slow"
    MEDIUM = "medium"
    FAST = "fast"

class MainChallenge(str, Enum):
    TRAFFIC = "traffic"
    CONVERSION = "conversion"
    RETENTION = "retention"
    CONTENT = "content"
    UNKNOWN = "unknown"

class TimeCommitment(str, Enum):
    LESS_THAN_2 = "less_than_2"
    TWO_TO_5 = "2_to_5"
    FIVE_TO_10 = "5_to_10"
    FULL_TIME = "full_time"

class Budget(str, Enum):
    LESS_THAN_100 = "less_than_100"
    ONE_HUNDRED_TO_500 = "100_to_500"
    FIVE_HUNDRED_TO_1000 = "500_to_1000"
    MORE_THAN_1000 = "more_than_1000"

class QuizAnswers(BaseModel):
    business_type: str
    business_stage: str
    growth_speed: str
    main_challenge: str
    time_commitment: str
    budget: str
    business_description: str
    primary_goal: str

class GrowthPlanSummary(BaseModel):
    title: str
    description: str
    key_tactics: List[str]
    estimated_cost: float
    estimated_time_hours: int
    success_probability: float

# Auth Models
class UserRegister(BaseModel):
    name: str
    email: str
    password: str

class UserLogin(BaseModel):
    email: str
    password: str

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    created_at: str

# Simple configuration for testing
class Settings:
    APP_NAME = "Growlytics"
    APP_VERSION = "1.0.0"
    DEBUG = True
    CORS_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]

settings = Settings()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 Starting Growlytics Backend...")
    print(f"📊 Environment: {'Development' if settings.DEBUG else 'Production'}")
    yield
    # Shutdown
    print("🛑 Shutting down Growlytics Backend...")

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    description="Backend API for Growlytics - Growth Plan Generator for Small Businesses",
    version=settings.APP_VERSION,
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple AI function
def get_growth_plan(quiz_answers: QuizAnswers) -> GrowthPlanSummary:
    """Generate growth plan based on business stage"""
    stage_plans = {
        "idea": {
            "title": "خطة إطلاق المشروع",
            "description": "خطة شاملة لتحويل فكرتك إلى مشروع حقيقي",
            "key_tactics": ["بحث السوق", "بناء MVP", "اختبار الفكرة", "بناء الجمهور", "التحضير للإطلاق"],
            "estimated_cost": 300.0,
            "estimated_time_hours": 60,
            "success_probability": 0.7
        },
        "launch": {
            "title": "خطة الإطلاق والانتشار",
            "description": "خطة لإطلاق مشروعك بنجاح وجذب العملاء الأوائل",
            "key_tactics": ["إطلاق رسمي", "تسويق المحتوى", "وسائل التواصل", "شراكات", "عروض الإطلاق"],
            "estimated_cost": 500.0,
            "estimated_time_hours": 50,
            "success_probability": 0.75
        },
        "profit": {
            "title": "خطة تحسين الربحية",
            "description": "خطة لزيادة الأرباح وتحسين العمليات",
            "key_tactics": ["تحسين التحويل", "زيادة متوسط الطلب", "برامج الولاء", "تحسين التسعير", "خفض التكاليف"],
            "estimated_cost": 800.0,
            "estimated_time_hours": 45,
            "success_probability": 0.8
        },
        "growth": {
            "title": "خطة النمو المتسارع",
            "description": "خطة لتسريع نمو مشروعك والوصول لعملاء جدد",
            "key_tactics": ["الإعلانات المدفوعة", "التسويق بالمحتوى", "الشراكات الاستراتيجية", "توسيع المنتجات", "أتمتة العمليات"],
            "estimated_cost": 1200.0,
            "estimated_time_hours": 55,
            "success_probability": 0.85
        },
        "scale": {
            "title": "خطة التوسع والانتشار",
            "description": "خطة للتوسع في أسواق جديدة وزيادة الحصة السوقية",
            "key_tactics": ["دخول أسواق جديدة", "بناء فريق المبيعات", "الاستثمار في التكنولوجيا", "الشراكات الدولية", "تطوير المنتجات"],
            "estimated_cost": 2000.0,
            "estimated_time_hours": 70,
            "success_probability": 0.9
        }
    }

    plan_data = stage_plans.get(quiz_answers.business_stage, stage_plans["launch"])
    return GrowthPlanSummary(**plan_data)

# Simple API endpoint for growth plans
@app.post("/api/growth-plans/generate-summary", response_model=GrowthPlanSummary)
async def generate_growth_plan_summary(quiz_answers: QuizAnswers):
    """Generate growth plan summary from quiz answers"""
    try:
        summary = get_growth_plan(quiz_answers)
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate growth plan: {str(e)}")

# Auth endpoints
@app.post("/api/auth/register", response_model=UserResponse)
async def register_user(user_data: UserRegister):
    """Register a new user"""
    try:
        # Simple mock registration - in real app, save to database
        user_response = UserResponse(
            id="user_123",
            name=user_data.name,
            email=user_data.email,
            created_at="2024-01-01T00:00:00Z"
        )
        return user_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/api/auth/login", response_model=UserResponse)
async def login_user(user_data: UserLogin):
    """Login user"""
    try:
        # Simple mock login - in real app, verify credentials
        user_response = UserResponse(
            id="user_123",
            name="مستخدم تجريبي",
            email=user_data.email,
            created_at="2024-01-01T00:00:00Z"
        )
        return user_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/api/auth/me", response_model=UserResponse)
async def get_current_user():
    """Get current user info"""
    try:
        # Simple mock user - in real app, get from token
        user_response = UserResponse(
            id="user_123",
            name="مستخدم تجريبي",
            email="<EMAIL>",
            created_at="2024-01-01T00:00:00Z"
        )
        return user_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user: {str(e)}")

# Simple test endpoints for now
@app.get("/api/test")
async def test_endpoint():
    return {"message": "Backend is working!", "status": "success"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": "development" if settings.DEBUG else "production"
    }

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "مرحباً بك في Growlytics API",
        "description": "أداة توليد خطط النمو للمشاريع الصغيرة",
        "docs": "/docs" if settings.DEBUG else "Documentation not available in production",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
