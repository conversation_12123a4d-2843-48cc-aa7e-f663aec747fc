from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
from typing import List
from enum import Enum

# Simple Models
class BusinessStage(str, Enum):
    IDEA = "idea"
    LAUNCH = "launch"
    PROFIT = "profit"
    GROWTH = "growth"
    SCALE = "scale"

class GrowthSpeed(str, Enum):
    SLOW = "slow"
    MEDIUM = "medium"
    FAST = "fast"

class MainChallenge(str, Enum):
    TRAFFIC = "traffic"
    CONVERSION = "conversion"
    RETENTION = "retention"
    CONTENT = "content"
    UNKNOWN = "unknown"

class TimeCommitment(str, Enum):
    LESS_THAN_2 = "less_than_2"
    TWO_TO_5 = "2_to_5"
    FIVE_TO_10 = "5_to_10"
    FULL_TIME = "full_time"

class Budget(str, Enum):
    LESS_THAN_100 = "less_than_100"
    ONE_HUNDRED_TO_500 = "100_to_500"
    FIVE_HUNDRED_TO_1000 = "500_to_1000"
    MORE_THAN_1000 = "more_than_1000"

class QuizAnswers(BaseModel):
    business_type: str
    business_stage: str
    growth_speed: str
    main_challenge: str
    time_commitment: str
    budget: str
    business_description: str
    primary_goal: str

class GrowthPlanSummary(BaseModel):
    title: str
    description: str
    key_tactics: List[str]
    estimated_cost: float
    estimated_time_hours: int
    success_probability: float

# Auth Models
class UserRegister(BaseModel):
    email: str
    password: str
    full_name: str
    phone: str = None

class UserLogin(BaseModel):
    email: str
    password: str

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    created_at: str

# Simple configuration for testing
class Settings:
    APP_NAME = "Growlytics"
    APP_VERSION = "1.0.0"
    DEBUG = True
    CORS_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]

settings = Settings()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 Starting Growlytics Backend...")
    print(f"📊 Environment: {'Development' if settings.DEBUG else 'Production'}")
    yield
    # Shutdown
    print("🛑 Shutting down Growlytics Backend...")

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    description="Backend API for Growlytics - Growth Plan Generator for Small Businesses",
    version=settings.APP_VERSION,
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple AI function
def get_growth_plan(quiz_answers: QuizAnswers) -> GrowthPlanSummary:
    """Generate customized growth plan based on quiz answers"""

    # Get business details
    business_type = quiz_answers.business_type
    business_stage = quiz_answers.business_stage
    budget = quiz_answers.budget
    time_commitment = quiz_answers.time_commitment
    main_challenge = quiz_answers.main_challenge

    # Customize tactics based on business type and challenges
    tactics = get_customized_tactics(business_type, business_stage, main_challenge, budget)

    # Customize costs based on budget
    cost = get_customized_cost(budget, len(tactics))

    # Customize time based on commitment
    time_hours = get_customized_time(time_commitment, len(tactics))

    # Generate customized title and description
    title = f"خطة نمو {business_type} - مرحلة {get_stage_name(business_stage)}"
    description = f"خطة مخصصة لتطوير {business_type} والتغلب على تحدي {get_challenge_name(main_challenge)}"

    return GrowthPlanSummary(
        title=title,
        description=description,
        key_tactics=tactics,
        estimated_cost=cost,
        estimated_time_hours=time_hours,
        success_probability=0.8
    )

def get_customized_tactics(business_type: str, stage: str, challenge: str, budget: str) -> List[str]:
    """Get tactics based on business specifics"""

    # Base tactics for each stage
    stage_tactics = {
        "idea": ["بحث السوق", "اختبار الفكرة", "بناء MVP", "بناء الجمهور"],
        "launch": ["إطلاق رسمي", "تسويق المحتوى", "وسائل التواصل", "عروض الإطلاق"],
        "profit": ["تحسين التحويل", "برامج الولاء", "تحسين التسعير", "تسويق المحتوى"],
        "growth": ["الإعلانات المدفوعة", "الشراكات", "توسيع المنتجات", "أتمتة العمليات"],
        "scale": ["دخول أسواق جديدة", "بناء فريق المبيعات", "الشراكات الدولية", "الاستثمار في التكنولوجيا"]
    }

    tactics = stage_tactics.get(stage, stage_tactics["launch"])

    # Add challenge-specific tactics
    if challenge == "traffic":
        tactics.append("تحسين SEO")
    elif challenge == "conversion":
        tactics.append("تحسين صفحة الهبوط")
    elif challenge == "retention":
        tactics.append("برامج الولاء")
    elif challenge == "content":
        tactics.append("استراتيجية المحتوى")

    # Adjust for budget
    if budget == "less_than_100":
        # Focus on free/low-cost tactics
        tactics = [t for t in tactics if t not in ["الإعلانات المدفوعة", "الاستثمار في التكنولوجيا"]]
        if "تسويق مجاني" not in tactics:
            tactics.append("تسويق مجاني")

    return tactics[:5]  # Limit to 5 tactics

def get_customized_cost(budget: str, num_tactics: int) -> float:
    """Calculate cost based on budget and tactics"""
    budget_multipliers = {
        "less_than_100": 50,
        "100_to_500": 200,
        "500_to_1000": 600,
        "more_than_1000": 1200
    }
    base_cost = budget_multipliers.get(budget, 200)
    return base_cost * (num_tactics / 5)

def get_customized_time(time_commitment: str, num_tactics: int) -> int:
    """Calculate time based on commitment and tactics"""
    time_multipliers = {
        "less_than_2": 20,
        "2_to_5": 35,
        "5_to_10": 50,
        "full_time": 80
    }
    base_time = time_multipliers.get(time_commitment, 35)
    return int(base_time * (num_tactics / 5))

def get_stage_name(stage: str) -> str:
    """Get Arabic name for stage"""
    names = {
        "idea": "الفكرة",
        "launch": "الإطلاق",
        "profit": "الربحية",
        "growth": "النمو",
        "scale": "التوسع"
    }
    return names.get(stage, "الإطلاق")

def get_challenge_name(challenge: str) -> str:
    """Get Arabic name for challenge"""
    names = {
        "traffic": "جذب الزوار",
        "conversion": "تحويل الزوار لعملاء",
        "retention": "الاحتفاظ بالعملاء",
        "content": "إنشاء المحتوى",
        "unknown": "التحديات العامة"
    }
    return names.get(challenge, "التحديات العامة")

# Simple API endpoint for growth plans
@app.post("/api/growth-plans/generate-summary", response_model=GrowthPlanSummary)
async def generate_growth_plan_summary(quiz_answers: QuizAnswers):
    """Generate growth plan summary from quiz answers"""
    try:
        summary = get_growth_plan(quiz_answers)
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate growth plan: {str(e)}")

# Auth endpoints
@app.post("/api/auth/register")
async def register_user(user_data: UserRegister):
    """Register a new user"""
    try:
        # Simple mock registration - in real app, save to database
        user_response = {
            "user": {
                "id": "user_123",
                "name": user_data.full_name,
                "email": user_data.email,
                "created_at": "2024-01-01T00:00:00Z"
            },
            "access_token": "mock_token_123"
        }
        return user_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/api/auth/login")
async def login_user(user_data: UserLogin):
    """Login user"""
    try:
        # Simple mock login - in real app, verify credentials
        user_response = {
            "user": {
                "id": "user_123",
                "name": "مستخدم تجريبي",
                "email": user_data.email,
                "created_at": "2024-01-01T00:00:00Z"
            },
            "access_token": "mock_token_123"
        }
        return user_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/api/auth/me", response_model=UserResponse)
async def get_current_user():
    """Get current user info"""
    try:
        # Simple mock user - in real app, get from token
        user_response = UserResponse(
            id="user_123",
            name="مستخدم تجريبي",
            email="<EMAIL>",
            created_at="2024-01-01T00:00:00Z"
        )
        return user_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user: {str(e)}")

# Simple test endpoints for now
@app.get("/api/test")
async def test_endpoint():
    return {"message": "Backend is working!", "status": "success"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": "development" if settings.DEBUG else "production"
    }

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "مرحباً بك في Growlytics API",
        "description": "أداة توليد خطط النمو للمشاريع الصغيرة",
        "docs": "/docs" if settings.DEBUG else "Documentation not available in production",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
