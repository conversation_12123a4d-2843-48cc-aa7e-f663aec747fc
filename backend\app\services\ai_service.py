import requests
import json
import os
from typing import List, Dict, Any, Optional
from app.core.config import settings
from app.models.growth_plan import QuizAnswers, GrowthPlanSummary, BusinessStage
from app.models.tactic import TacticResponse
import logging

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        self.hf_token = os.getenv("HUGGINGFACE_API_KEY")
        self.api_url = "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium"
        if not self.hf_token:
            logger.warning("Hugging Face API key not found. Using fallback responses.")
    
    async def generate_growth_plan(self, quiz_answers: QuizAnswers) -> GrowthPlanSummary:
        """Generate a growth plan based on quiz answers"""
        try:
            # For now, use fallback plan for faster response
            # TODO: Implement proper HF integration when ready
            return self._get_fallback_growth_plan(quiz_answers)

        except Exception as e:
            logger.error(f"Error generating growth plan: {e}")
            return self._get_fallback_growth_plan(quiz_answers)
    
    async def explain_tactic(self, tactic: TacticResponse, user_context: Dict[str, Any]) -> str:
        """Explain a tactic in simple terms based on user context"""
        try:
            if self.hf_token:
                prompt = f"اشرح التكتيك: {tactic.title} - {tactic.short_description}"
                response = await self._call_huggingface_api(prompt)
                if response:
                    return response

            # Fallback explanation
            return f"هذا التكتيك ({tactic.title}) يساعدك في {tactic.short_description}. يمكنك تطبيقه من خلال اتباع الخطوات المذكورة."

        except Exception as e:
            logger.error(f"Error explaining tactic: {e}")
            return f"هذا التكتيك ({tactic.title}) يساعدك في {tactic.short_description}. يمكنك تطبيقه من خلال اتباع الخطوات المذكورة."
    
    async def chat_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generate AI chat response"""
        try:
            if self.hf_token:
                response = await self._call_huggingface_api(message)
                if response:
                    return response

            # Fallback response
            return "شكراً لسؤالك! أنا هنا لمساعدتك في تطوير مشروعك. يمكنني مساعدتك في فهم التكتيكات التسويقية وتطبيقها على نشاطك التجاري."

        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            return "عذراً، حدث خطأ في النظام. يرجى المحاولة مرة أخرى."

    async def _call_huggingface_api(self, prompt: str) -> str:
        """Call Hugging Face API"""
        try:
            headers = {"Authorization": f"Bearer {self.hf_token}"}
            data = {"inputs": prompt, "parameters": {"max_length": 500, "temperature": 0.7}}

            response = requests.post(self.api_url, headers=headers, json=data)
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get("generated_text", "")
                return str(result)
            else:
                logger.error(f"Hugging Face API error: {response.status_code}")
                return ""
        except Exception as e:
            logger.error(f"Error calling Hugging Face API: {e}")
            return ""

    def _create_growth_plan_prompt(self, quiz_answers: QuizAnswers) -> str:
        """Create prompt for growth plan generation"""
        return f"""
        قم بإنشاء خطة نمو مخصصة لهذا المشروع:
        
        نوع النشاط: {quiz_answers.business_type}
        المرحلة: {quiz_answers.business_stage.value}
        سرعة النمو المرغوبة: {quiz_answers.growth_speed.value}
        التحدي الأكبر: {quiz_answers.main_challenge.value}
        الوقت المتاح أسبوعياً: {quiz_answers.time_commitment.value}
        الميزانية الشهرية: {quiz_answers.budget.value}
        وصف النشاط: {quiz_answers.business_description}
        الهدف الأساسي: {quiz_answers.primary_goal}
        
        أريد خطة تحتوي على:
        1. عنوان جذاب للخطة
        2. وصف مختصر للخطة
        3. 5 تكتيكات رئيسية مناسبة
        4. تقدير التكلفة الإجمالية بالدولار
        5. تقدير الوقت المطلوب بالساعات
        6. نسبة احتمالية النجاح
        
        اجعل الإجابة في صيغة JSON مع هذا التنسيق:
        {{
            "title": "عنوان الخطة",
            "description": "وصف الخطة",
            "key_tactics": ["تكتيك 1", "تكتيك 2", "تكتيك 3", "تكتيك 4", "تكتيك 5"],
            "estimated_cost": 500.0,
            "estimated_time_hours": 40,
            "success_probability": 0.75
        }}
        """
    
    def _parse_growth_plan_response(self, content: str, quiz_answers: QuizAnswers) -> GrowthPlanSummary:
        """Parse AI response into GrowthPlanSummary"""
        try:
            # Try to extract JSON from response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                data = json.loads(json_str)
                return GrowthPlanSummary(**data)
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
        
        # Fallback
        return self._get_fallback_growth_plan(quiz_answers)
    
    def _get_fallback_growth_plan(self, quiz_answers: QuizAnswers) -> GrowthPlanSummary:
        """Get fallback growth plan when AI fails"""
        stage_plans = {
            BusinessStage.IDEA: {
                "title": "خطة إطلاق المشروع",
                "description": "خطة شاملة لتحويل فكرتك إلى مشروع حقيقي",
                "key_tactics": ["بحث السوق", "بناء MVP", "اختبار الفكرة", "بناء الجمهور", "التحضير للإطلاق"],
                "estimated_cost": 300.0,
                "estimated_time_hours": 60,
                "success_probability": 0.7
            },
            BusinessStage.LAUNCH: {
                "title": "خطة الإطلاق والانتشار",
                "description": "خطة لإطلاق مشروعك بنجاح وجذب العملاء الأوائل",
                "key_tactics": ["إطلاق رسمي", "تسويق المحتوى", "وسائل التواصل", "شراكات", "عروض الإطلاق"],
                "estimated_cost": 500.0,
                "estimated_time_hours": 50,
                "success_probability": 0.75
            },
            BusinessStage.PROFIT: {
                "title": "خطة تحسين الربحية",
                "description": "خطة لزيادة الأرباح وتحسين العمليات",
                "key_tactics": ["تحسين التحويل", "زيادة متوسط الطلب", "برامج الولاء", "تحسين التسعير", "خفض التكاليف"],
                "estimated_cost": 800.0,
                "estimated_time_hours": 45,
                "success_probability": 0.8
            },
            BusinessStage.GROWTH: {
                "title": "خطة النمو المتسارع",
                "description": "خطة لتسريع نمو مشروعك والوصول لعملاء جدد",
                "key_tactics": ["الإعلانات المدفوعة", "التسويق بالمحتوى", "الشراكات الاستراتيجية", "توسيع المنتجات", "أتمتة العمليات"],
                "estimated_cost": 1200.0,
                "estimated_time_hours": 55,
                "success_probability": 0.85
            },
            BusinessStage.SCALE: {
                "title": "خطة التوسع والانتشار",
                "description": "خطة للتوسع في أسواق جديدة وزيادة الحصة السوقية",
                "key_tactics": ["دخول أسواق جديدة", "بناء فريق المبيعات", "الاستثمار في التكنولوجيا", "الشراكات الدولية", "تطوير المنتجات"],
                "estimated_cost": 2000.0,
                "estimated_time_hours": 70,
                "success_probability": 0.9
            }
        }
        
        plan_data = stage_plans.get(quiz_answers.business_stage, stage_plans[BusinessStage.LAUNCH])
        return GrowthPlanSummary(**plan_data)

# Create global instance
ai_service = AIService()
