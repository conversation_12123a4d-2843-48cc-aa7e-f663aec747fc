import openai
from typing import List, Dict, Any, Optional
from app.core.config import settings
from app.models.growth_plan import QuizAnswers, GrowthPlanSummary, BusinessStage
from app.models.tactic import TacticResponse
import json
import logging

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        if settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY
        else:
            logger.warning("OpenAI API key not found. AI features will not work.")
    
    async def generate_growth_plan(self, quiz_answers: QuizAnswers) -> GrowthPlanSummary:
        """Generate a growth plan based on quiz answers"""
        try:
            prompt = self._create_growth_plan_prompt(quiz_answers)
            
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "أنت خبير في استراتيجيات النمو للمشاريع الصغيرة. قم بإنشاء خطة نمو مخصصة باللغة العربية."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=settings.MAX_TOKENS,
                temperature=settings.TEMPERATURE
            )
            
            content = response.choices[0].message.content
            return self._parse_growth_plan_response(content, quiz_answers)
            
        except Exception as e:
            logger.error(f"Error generating growth plan: {e}")
            return self._get_fallback_growth_plan(quiz_answers)
    
    async def explain_tactic(self, tactic: TacticResponse, user_context: Dict[str, Any]) -> str:
        """Explain a tactic in simple terms based on user context"""
        try:
            prompt = f"""
            اشرح هذا التكتيك التسويقي بطريقة بسيطة ومفهومة:
            
            التكتيك: {tactic.title}
            الوصف: {tactic.description}
            الخطوات: {', '.join(tactic.steps)}
            
            معلومات المستخدم:
            - نوع النشاط: {user_context.get('business_type', 'غير محدد')}
            - المرحلة: {user_context.get('business_stage', 'غير محدد')}
            - الميزانية: {user_context.get('budget', 'غير محدد')}
            
            اشرح كيف يمكن تطبيق هذا التكتيك على نشاطه التجاري بطريقة عملية.
            """
            
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "أنت مستشار تسويق خبير. اشرح التكتيكات بطريقة بسيطة وعملية."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error explaining tactic: {e}")
            return f"هذا التكتيك ({tactic.title}) يساعدك في {tactic.short_description}. يمكنك تطبيقه من خلال اتباع الخطوات المذكورة."
    
    async def chat_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generate AI chat response"""
        try:
            system_prompt = """
            أنت مساعد ذكي متخصص في مساعدة أصحاب المشاريع الصغيرة في النمو والتسويق.
            - أجب باللغة العربية
            - كن مفيداً وعملياً
            - قدم نصائح قابلة للتطبيق
            - اسأل أسئلة توضيحية عند الحاجة
            """
            
            response = await openai.ChatCompletion.acreate(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                max_tokens=400,
                temperature=0.8
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            return "عذراً، حدث خطأ في النظام. يرجى المحاولة مرة أخرى."
    
    def _create_growth_plan_prompt(self, quiz_answers: QuizAnswers) -> str:
        """Create prompt for growth plan generation"""
        return f"""
        قم بإنشاء خطة نمو مخصصة لهذا المشروع:
        
        نوع النشاط: {quiz_answers.business_type}
        المرحلة: {quiz_answers.business_stage.value}
        سرعة النمو المرغوبة: {quiz_answers.growth_speed.value}
        التحدي الأكبر: {quiz_answers.main_challenge.value}
        الوقت المتاح أسبوعياً: {quiz_answers.time_commitment.value}
        الميزانية الشهرية: {quiz_answers.budget.value}
        وصف النشاط: {quiz_answers.business_description}
        الهدف الأساسي: {quiz_answers.primary_goal}
        
        أريد خطة تحتوي على:
        1. عنوان جذاب للخطة
        2. وصف مختصر للخطة
        3. 5 تكتيكات رئيسية مناسبة
        4. تقدير التكلفة الإجمالية بالدولار
        5. تقدير الوقت المطلوب بالساعات
        6. نسبة احتمالية النجاح
        
        اجعل الإجابة في صيغة JSON مع هذا التنسيق:
        {{
            "title": "عنوان الخطة",
            "description": "وصف الخطة",
            "key_tactics": ["تكتيك 1", "تكتيك 2", "تكتيك 3", "تكتيك 4", "تكتيك 5"],
            "estimated_cost": 500.0,
            "estimated_time_hours": 40,
            "success_probability": 0.75
        }}
        """
    
    def _parse_growth_plan_response(self, content: str, quiz_answers: QuizAnswers) -> GrowthPlanSummary:
        """Parse AI response into GrowthPlanSummary"""
        try:
            # Try to extract JSON from response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                data = json.loads(json_str)
                return GrowthPlanSummary(**data)
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
        
        # Fallback
        return self._get_fallback_growth_plan(quiz_answers)
    
    def _get_fallback_growth_plan(self, quiz_answers: QuizAnswers) -> GrowthPlanSummary:
        """Get fallback growth plan when AI fails"""
        stage_plans = {
            BusinessStage.IDEA: {
                "title": "خطة إطلاق المشروع",
                "description": "خطة شاملة لتحويل فكرتك إلى مشروع حقيقي",
                "key_tactics": ["بحث السوق", "بناء MVP", "اختبار الفكرة", "بناء الجمهور", "التحضير للإطلاق"],
                "estimated_cost": 300.0,
                "estimated_time_hours": 60,
                "success_probability": 0.7
            },
            BusinessStage.LAUNCH: {
                "title": "خطة الإطلاق والانتشار",
                "description": "خطة لإطلاق مشروعك بنجاح وجذب العملاء الأوائل",
                "key_tactics": ["إطلاق رسمي", "تسويق المحتوى", "وسائل التواصل", "شراكات", "عروض الإطلاق"],
                "estimated_cost": 500.0,
                "estimated_time_hours": 50,
                "success_probability": 0.75
            },
            BusinessStage.PROFIT: {
                "title": "خطة تحسين الربحية",
                "description": "خطة لزيادة الأرباح وتحسين العمليات",
                "key_tactics": ["تحسين التحويل", "زيادة متوسط الطلب", "برامج الولاء", "تحسين التسعير", "خفض التكاليف"],
                "estimated_cost": 800.0,
                "estimated_time_hours": 45,
                "success_probability": 0.8
            },
            BusinessStage.GROWTH: {
                "title": "خطة النمو المتسارع",
                "description": "خطة لتسريع نمو مشروعك والوصول لعملاء جدد",
                "key_tactics": ["الإعلانات المدفوعة", "التسويق بالمحتوى", "الشراكات الاستراتيجية", "توسيع المنتجات", "أتمتة العمليات"],
                "estimated_cost": 1200.0,
                "estimated_time_hours": 55,
                "success_probability": 0.85
            },
            BusinessStage.SCALE: {
                "title": "خطة التوسع والانتشار",
                "description": "خطة للتوسع في أسواق جديدة وزيادة الحصة السوقية",
                "key_tactics": ["دخول أسواق جديدة", "بناء فريق المبيعات", "الاستثمار في التكنولوجيا", "الشراكات الدولية", "تطوير المنتجات"],
                "estimated_cost": 2000.0,
                "estimated_time_hours": 70,
                "success_probability": 0.9
            }
        }
        
        plan_data = stage_plans.get(quiz_answers.business_stage, stage_plans[BusinessStage.LAUNCH])
        return GrowthPlanSummary(**plan_data)

# Create global instance
ai_service = AIService()
