import { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { motion } from 'framer-motion'
import { 
  CheckIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowLeftIcon,
  PlayIcon,
  LightBulbIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'
import { useAuth } from '@/hooks/useAuth'

interface DetailedTactic {
  title: string
  description: string
  steps: string[]
  tools: string[]
  timeRequired: string
  difficulty: 'سهل' | 'متوسط' | 'صعب'
  cost: string
  tips: string[]
  expectedResults: string[]
}

interface PlanSummary {
  title: string
  description: string
  key_tactics: string[]
  estimated_cost: number
  estimated_time_hours: number
  success_probability: number
}

export default function DetailedPlan() {
  const [planSummary, setPlanSummary] = useState<PlanSummary | null>(null)
  const [detailedTactics, setDetailedTactics] = useState<DetailedTactic[]>([])
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    // Get quiz answers from localStorage and generate fresh plan
    const storedAnswers = localStorage.getItem('quizAnswers')

    if (storedAnswers) {
      try {
        const answers = JSON.parse(storedAnswers)
        generateFreshPlan(answers)
      } catch (error) {
        console.error('Error parsing quiz answers:', error)
        router.push('/dashboard')
      }
    } else {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const generateFreshPlan = async (quizAnswers: any) => {
    try {
      // Call backend to generate fresh plan
      const response = await fetch('http://localhost:8000/api/growth-plans/generate-summary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(quizAnswers)
      })

      if (response.ok) {
        const summary = await response.json()
        setPlanSummary(summary)
        generateDetailedTactics(summary, quizAnswers)
      } else {
        // Fallback to stored summary if API fails
        const storedSummary = localStorage.getItem('planSummary')
        if (storedSummary) {
          const summary = JSON.parse(storedSummary)
          setPlanSummary(summary)
          generateDetailedTactics(summary, quizAnswers)
        }
      }
    } catch (error) {
      console.error('Error generating fresh plan:', error)
      // Fallback to stored summary
      const storedSummary = localStorage.getItem('planSummary')
      if (storedSummary) {
        const summary = JSON.parse(storedSummary)
        setPlanSummary(summary)
        generateDetailedTactics(summary, quizAnswers)
      }
    }
  }

  const generateDetailedTactics = (summary: PlanSummary, quizAnswers: any) => {
    // Generate detailed tactics based on the summary and quiz answers
    const tactics: DetailedTactic[] = summary.key_tactics.map((tactic, index) => ({
      title: tactic,
      description: getDetailedDescription(tactic, quizAnswers),
      steps: getDetailedSteps(tactic, quizAnswers),
      tools: getRequiredTools(tactic, quizAnswers),
      timeRequired: getTimeRequired(index),
      difficulty: getDifficulty(index),
      cost: getCost(index),
      tips: getTips(tactic, quizAnswers),
      expectedResults: getExpectedResults(tactic, quizAnswers)
    }))

    setDetailedTactics(tactics)
  }

  const getDetailedDescription = (tactic: string, quizAnswers: any): string => {
    const businessType = quizAnswers?.business_type || 'مشروع'
    const businessDescription = quizAnswers?.business_description || ''

    const descriptions: { [key: string]: string } = {
      'إطلاق رسمي': `الإعلان الرسمي عن ${businessType} وبدء العمليات التجارية. ${businessDescription ? `خاص بـ ${businessDescription}` : ''}`,
      'تسويق المحتوى': `إنشاء محتوى قيم يجذب عملاء ${businessType} ويبني الثقة في علامتك التجارية`,
      'وسائل التواصل': `استخدام منصات التواصل للوصول لعملاء ${businessType} والتفاعل معهم بطريقة تناسب طبيعة عملك`,
      'شراكات': `بناء علاقات تجارية مع شركات تخدم نفس عملاء ${businessType} للاستفادة المتبادلة`,
      'عروض الإطلاق': `تقديم عروض خاصة لـ ${businessType} لجذب العملاء الأوائل وبناء قاعدة عملاء`
    }
    return descriptions[tactic] || `تكتيك مخصص لتطوير ${businessType} وتحقيق أهدافك التجارية`
  }

  const getDetailedSteps = (tactic: string, quizAnswers: any): string[] => {
    const businessType = quizAnswers?.business_type || 'مشروعك'
    const budget = quizAnswers?.budget || 'محدودة'
    const timeCommitment = quizAnswers?.time_commitment || 'محدود'
    const steps: { [key: string]: string[] } = {
      'إطلاق رسمي': [
        `اختر تاريخ الإطلاق لـ ${businessType} واعمل عد تنازلي (7-14 يوم)`,
        `حضر بيان صحفي يشرح كيف ${businessType} يحل مشكلة حقيقية للعملاء`,
        `أنشئ صفحة هبوط تركز على الفائدة الرئيسية لـ ${businessType}`,
        `راسل المؤثرين والصحفيين المتخصصين في مجال ${businessType}`,
        `انشر الإعلان مع التركيز على الجمهور المستهدف لـ ${businessType}`,
        `نظم حدث إطلاق ${budget.includes('less_than_100') ? '(أونلاين مجاني)' : '(أونلاين أو أوفلاين)'}`,
        `تابع التغطية ورد على استفسارات العملاء المهتمين بـ ${businessType}`
      ],
      'تسويق المحتوى': [
        `ادرس عملاء ${businessType}: ما مشاكلهم؟ أين يبحثون عن حلول؟`,
        `اختر مواضيع تهم عملاء ${businessType}: نصائح، حلول، قصص نجاح`,
        `خطط لـ ${timeCommitment.includes('less_than_2') ? '10' : '20'} منشور شهرياً حسب وقتك المتاح`,
        `اكتب محتوى يحل مشاكل عملاء ${businessType} بطريقة عملية`,
        `استخدم Canva لتصميم صور تناسب طبيعة ${businessType}`,
        `انشر في أوقات تواجد عملاء ${businessType} أونلاين`,
        `رد على تعليقات العملاء المهتمين بـ ${businessType} بسرعة`,
        `قس أي محتوى يجذب عملاء ${businessType} أكثر وركز عليه`
      ],
      'وسائل التواصل': [
        'اختر منصتين فقط للبداية (فيسبوك + إنستجرام مثلاً)',
        'أنشئ حسابات تجارية مع صور بروفايل وكوفر احترافية',
        'اكتب وصف واضح يشرح ما تقدمه في 3 جمل',
        'انشر محتوى يومياً: صور منتجات، نصائح، قصص عملاء',
        'استخدم هاشتاجات مناسبة (10-15 هاشتاج لكل منشور)',
        'تفاعل مع منشورات العملاء والمنافسين',
        'اعمل إعلانات مدفوعة بميزانية 5-10$ يومياً',
        'رد على الرسائل خلال ساعتين كحد أقصى'
      ],
      'شراكات': [
        'اعمل قائمة بـ 20 شركة/مؤثر يخدمون نفس جمهورك',
        'ادرس كل شريك محتمل: جمهوره، قيمه، طريقة عمله',
        'حضر عرض شراكة واضح: ماذا ستقدم؟ ماذا تريد؟',
        'ابدأ بشراكات صغيرة: تبادل منشورات، خصومات متبادلة',
        'راسل 5 شركاء أسبوعياً برسائل شخصية',
        'اقترح أفكار تعاون محددة: ورش عمل مشتركة، عروض مجمعة',
        'وثق الاتفاقيات كتابياً حتى لو بسيطة',
        'قيم نتائج كل شراكة وطور العلاقات الناجحة'
      ],
      'عروض الإطلاق': [
        'حدد نوع العرض: خصم 30%، اشتري واحد واحصل على آخر مجاناً',
        'اجعل العرض محدود الوقت (3-7 أيام فقط)',
        'صمم صور إعلانية جذابة تبرز قيمة العرض',
        'أنشئ كود خصم سهل التذكر (LAUNCH30 مثلاً)',
        'اعلن عن العرض قبل بدايته بيومين لخلق ترقب',
        'انشر العرض على جميع منصاتك في نفس الوقت',
        'أرسل إيميلات لقائمة عملائك (إن وجدت)',
        'تابع المبيعات كل ساعة وحفز العملاء المترددين'
      ]
    }
    return steps[tactic] || [
      'خطط للتكتيك وحدد الأهداف الواضحة والقابلة للقياس',
      'اجمع الموارد والأدوات المطلوبة وتأكد من جاهزيتها',
      'نفذ الخطة خطوة بخطوة مع توثيق كل إجراء',
      'راقب النتائج يومياً وقس الأداء بمؤشرات واضحة',
      'حسن وطور التكتيك حسب النتائج والتغذية الراجعة'
    ]
  }

  const getRequiredTools = (tactic: string, quizAnswers: any): string[] => {
    const budget = quizAnswers?.budget || 'محدودة'
    const tools: { [key: string]: string[] } = {
      'إطلاق رسمي': [
        'Canva (تصميم البيان الصحفي والصور)',
        'Mailchimp (إرسال إيميلات للصحفيين)',
        'WordPress أو Wix (صفحة الهبوط)',
        'Zoom (حدث الإطلاق الأونلاين)',
        'Google Analytics (تتبع الزوار)'
      ],
      'تسويق المحتوى': budget.includes('less_than_100') ? [
        'Canva مجاني (تصميم صور بسيطة)',
        'Google Docs (كتابة المحتوى)',
        'Buffer مجاني (3 منصات)',
        'Unsplash (صور مجانية)',
        'Grammarly مجاني (تصحيح أساسي)'
      ] : [
        'Canva Pro (تصميم صور احترافية)',
        'Grammarly Premium (تصحيح متقدم)',
        'Buffer Pro (جدولة متقدمة)',
        'Google Docs (كتابة المحتوى)',
        'Adobe Stock (صور مدفوعة عالية الجودة)'
      ],
      'وسائل التواصل': [
        'Facebook Business Manager (إدارة الصفحات والإعلانات)',
        'Instagram Creator Studio (جدولة منشورات إنستجرام)',
        'Hootsuite (إدارة عدة حسابات)',
        'Canva (تصميم المنشورات)',
        'Hashtag Generator (إيجاد هاشتاجات مناسبة)'
      ],
      'شراكات': [
        'LinkedIn Sales Navigator (البحث عن شركاء)',
        'Gmail أو Outlook (التواصل المهني)',
        'DocuSign (توقيع الاتفاقيات)',
        'Calendly (تنسيق المواعيد)',
        'Google Sheets (تتبع الشراكات)'
      ],
      'عروض الإطلاق': [
        'Canva (تصميم إعلانات العروض)',
        'WooCommerce أو Shopify (أكواد الخصم)',
        'Mailchimp (إيميلات العروض)',
        'Facebook Ads Manager (إعلانات مدفوعة)',
        'Google Analytics (تتبع المبيعات)'
      ]
    }
    return tools[tactic] || [
      'أدوات تصميم مجانية (Canva)',
      'منصات التواصل الاجتماعي',
      'أدوات تحليل البيانات (Google Analytics)',
      'برامج إدارة المشاريع',
      'أدوات التواصل والإيميل'
    ]
  }

  const getTimeRequired = (index: number): string => {
    const times = ['1-2 أسابيع', '2-3 أسابيع', '1-2 أسابيع', '3-4 أسابيع', '1 أسبوع']
    return times[index] || '1-2 أسابيع'
  }

  const getDifficulty = (index: number): 'سهل' | 'متوسط' | 'صعب' => {
    const difficulties: ('سهل' | 'متوسط' | 'صعب')[] = ['سهل', 'متوسط', 'سهل', 'متوسط', 'سهل']
    return difficulties[index] || 'متوسط'
  }

  const getCost = (index: number): string => {
    const costs = ['مجاني - $50', '$50 - $200', 'مجاني - $100', '$100 - $300', 'مجاني - $50']
    return costs[index] || 'مجاني - $100'
  }

  const getTips = (tactic: string, quizAnswers: any): string[] => {
    const businessType = quizAnswers?.business_type || 'مشروعك'
    const budget = quizAnswers?.budget || 'محدودة'
    const tips: { [key: string]: string[] } = {
      'إطلاق رسمي': [
        '💡 اختر يوم الثلاثاء أو الأربعاء للإطلاق - أعلى معدلات تفاعل',
        '📱 حضر محتوى مختلف لكل منصة (فيديو لإنستجرام، مقال لفيسبوك)',
        '🎯 ركز على فائدة واحدة واضحة في رسالتك',
        '📧 تابع مع الصحفيين بعد 3 أيام إذا لم يردوا'
      ],
      'تسويق المحتوى': [
        `📝 اكتب عناوين تحل مشاكل عملاء ${businessType} مباشرة`,
        `📊 استخدم قاعدة 80/20: 80% نصائح مفيدة لـ ${businessType}، 20% ترويج`,
        `⏰ انشر عندما يكون عملاء ${businessType} أكثر نشاطاً أونلاين`,
        `💬 اطرح أسئلة عن تحديات ${businessType} لزيادة التفاعل`
      ],
      'وسائل التواصل': [
        '📸 استخدم صور حقيقية لمنتجك بدلاً من الصور المخزنة',
        '#️⃣ ابحث عن هاشتاجات بـ 10-100 ألف منشور (منافسة أقل)',
        '🤝 تفاعل مع منشورات عملائك - يزيد الولاء',
        '📈 جرب أنواع محتوى مختلفة واشوف أيها يعمل أفضل'
      ],
      'شراكات': [
        '🎁 ابدأ بتقديم قيمة قبل طلب أي شيء',
        '📋 اعمل بحث عن الشريك قبل التواصل - اذكر شيء محدد عنه',
        '📝 اقترح شراكة محددة بدلاً من "نتعاون"',
        '🔄 تابع كل أسبوعين مع الشركاء الحاليين'
      ],
      'عروض الإطلاق': [
        '⏰ اجعل العرض محدود الوقت حقاً - لا تمدده',
        '💰 اختبر خصومات مختلفة: 25% vs 30% vs "اشتري 2 بـ 1"',
        '📢 أعلن عن انتهاء العرض قبل 24 ساعة',
        '📊 تتبع مصدر كل عملية شراء لتعرف أي قناة تعمل أفضل'
      ]
    }
    return tips[tactic] || [
      '💡 ابدأ بخطوات صغيرة وقابلة للتنفيذ',
      '📊 قس النتائج باستمرار وعدل الخطة حسب الحاجة',
      '🎯 ركز على هدف واحد في كل مرة',
      '🤝 لا تتردد في طلب المساعدة من الخبراء'
    ]
  }

  const getExpectedResults = (tactic: string, quizAnswers: any): string[] => {
    const businessType = quizAnswers?.business_type || 'مشروعك'
    const results: { [key: string]: string[] } = {
      'إطلاق رسمي': [
        `📈 زيادة الوعي بـ ${businessType} بنسبة 40-60%`,
        `👥 جذب 100-500 عميل محتمل مهتم بـ ${businessType}`,
        `📰 تغطية إعلامية في منصات متخصصة في مجال ${businessType}`,
        `💬 زيادة المتابعين المهتمين بـ ${businessType} بنسبة 20-30%`
      ],
      'تسويق المحتوى': [
        '📊 زيادة التفاعل بنسبة 50-80%',
        '🔍 تحسن ترتيب موقعك في جوجل',
        '👥 بناء جمهور مخلص ومتفاعل',
        '💰 زيادة المبيعات بنسبة 15-25%'
      ],
      'وسائل التواصل': [
        '📱 نمو المتابعين بمعدل 10-20% شهرياً',
        '💬 زيادة الرسائل والاستفسارات',
        '🎯 وصول أفضل للعملاء المستهدفين',
        '🔄 زيادة زيارات الموقع من وسائل التواصل'
      ],
      'شراكات': [
        '🤝 بناء 3-5 شراكات قوية',
        '📈 زيادة المبيعات بنسبة 20-40%',
        '🌐 الوصول لأسواق جديدة',
        '💡 تعلم أفكار وتكتيكات جديدة'
      ],
      'عروض الإطلاق': [
        '💰 زيادة المبيعات بنسبة 100-300%',
        '👥 جذب 50-200 عميل جديد',
        '📧 بناء قائمة إيميل من 100-500 عميل',
        '🔄 زيادة معدل العملاء العائدين'
      ]
    }
    return results[tactic] || [
      '📈 تحسن عام في أداء المشروع',
      '👥 زيادة قاعدة العملاء',
      '💰 نمو في الإيرادات',
      '🎯 تحقيق الأهداف المحددة'
    ]
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'سهل': return 'text-green-600 bg-green-100'
      case 'متوسط': return 'text-yellow-600 bg-yellow-100'
      case 'صعب': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحضير الخطة التفصيلية...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <>
      <Head>
        <title>الخطة التفصيلية - Growlytics</title>
        <meta name="description" content="خطة النمو التفصيلية مع الخطوات والأدوات المطلوبة" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/dashboard">
                <Button variant="outline" className="mb-4">
                  <ArrowLeftIcon className="w-4 h-4 ml-2" />
                  العودة للوحة التحكم
                </Button>
              </Link>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                📋 الخطة التفصيلية
              </h1>
              <p className="text-gray-600">
                خطوات مفصلة لتنفيذ كل تكتيك في خطة النمو الخاصة بك
              </p>
            </motion.div>

            {planSummary && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="card mb-8"
              >
                <h2 className="text-xl font-bold text-gray-900 mb-4">
                  {planSummary.title}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <CurrencyDollarIcon className="w-5 h-5 text-green-600 mx-auto mb-1" />
                    <div className="font-bold text-gray-900">${planSummary.estimated_cost}</div>
                    <div className="text-xs text-gray-600">التكلفة الإجمالية</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <ClockIcon className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                    <div className="font-bold text-gray-900">{planSummary.estimated_time_hours} ساعة</div>
                    <div className="text-xs text-gray-600">الوقت المطلوب</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <ChartBarIcon className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                    <div className="font-bold text-gray-900">{Math.round(planSummary.success_probability * 100)}%</div>
                    <div className="text-xs text-gray-600">احتمالية النجاح</div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Detailed Tactics */}
            <div className="space-y-6">
              {detailedTactics.map((tactic, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  className="card"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {index + 1}. {tactic.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {tactic.description}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(tactic.difficulty)}`}>
                        {tactic.difficulty}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <ClockIcon className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{tactic.timeRequired}</div>
                      <div className="text-xs text-gray-600">المدة المطلوبة</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <CurrencyDollarIcon className="w-5 h-5 text-green-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{tactic.cost}</div>
                      <div className="text-xs text-gray-600">التكلفة المتوقعة</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <DocumentTextIcon className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{tactic.steps.length} خطوات</div>
                      <div className="text-xs text-gray-600">للتنفيذ</div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {/* Steps */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <PlayIcon className="w-4 h-4 text-primary-600 ml-2" />
                        خطوات التنفيذ:
                      </h4>
                      <div className="space-y-3">
                        {tactic.steps.map((step, stepIndex) => (
                          <div key={stepIndex} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                              <span className="text-xs font-medium text-primary-600">{stepIndex + 1}</span>
                            </div>
                            <span className="text-gray-700 text-sm leading-relaxed">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tools and Tips Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Tools */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                          <LightBulbIcon className="w-4 h-4 text-yellow-600 ml-2" />
                          الأدوات المطلوبة:
                        </h4>
                        <div className="space-y-2">
                          {tactic.tools.map((tool, toolIndex) => (
                            <div key={toolIndex} className="flex items-start space-x-2 p-2 bg-yellow-50 rounded">
                              <CheckIcon className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
                              <span className="text-gray-700 text-sm">{tool}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Tips */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                          💡 نصائح مهمة:
                        </h4>
                        <div className="space-y-2">
                          {tactic.tips.map((tip, tipIndex) => (
                            <div key={tipIndex} className="p-2 bg-blue-50 rounded text-sm text-gray-700">
                              {tip}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Expected Results */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        🎯 النتائج المتوقعة:
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {tactic.expectedResults.map((result, resultIndex) => (
                          <div key={resultIndex} className="flex items-center space-x-2 p-2 bg-green-50 rounded">
                            <span className="text-green-600">✅</span>
                            <span className="text-gray-700 text-sm">{result}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="card text-center mt-8 gradient-bg text-white"
            >
              <h3 className="text-xl font-bold mb-4">
                🚀 جاهز لبدء التنفيذ؟
              </h3>
              <p className="text-white/90 mb-6">
                ابدأ بالتكتيك الأول واتبع الخطوات المفصلة. تذكر أن النجاح يحتاج للصبر والمثابرة!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/dashboard">
                  <Button variant="secondary">
                    العودة للوحة التحكم
                  </Button>
                </Link>
                <Link href="/quiz">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                    إنشاء خطة جديدة
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </Layout>
    </>
  )
}
