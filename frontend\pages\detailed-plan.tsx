import { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { motion } from 'framer-motion'
import { 
  CheckIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowLeftIcon,
  PlayIcon,
  LightBulbIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'
import { useAuth } from '@/hooks/useAuth'

interface DetailedTactic {
  title: string
  description: string
  steps: string[]
  tools: string[]
  timeRequired: string
  difficulty: 'سهل' | 'متوسط' | 'صعب'
  cost: string
}

interface PlanSummary {
  title: string
  description: string
  key_tactics: string[]
  estimated_cost: number
  estimated_time_hours: number
  success_probability: number
}

export default function DetailedPlan() {
  const [planSummary, setPlanSummary] = useState<PlanSummary | null>(null)
  const [detailedTactics, setDetailedTactics] = useState<DetailedTactic[]>([])
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    // Get plan summary from localStorage
    const storedSummary = localStorage.getItem('planSummary')
    if (storedSummary) {
      try {
        const summary = JSON.parse(storedSummary)
        setPlanSummary(summary)
        generateDetailedTactics(summary)
      } catch (error) {
        console.error('Error parsing plan summary:', error)
        router.push('/dashboard')
      }
    } else {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const generateDetailedTactics = (summary: PlanSummary) => {
    // Generate detailed tactics based on the summary
    const tactics: DetailedTactic[] = summary.key_tactics.map((tactic, index) => ({
      title: tactic,
      description: getDetailedDescription(tactic),
      steps: getDetailedSteps(tactic),
      tools: getRequiredTools(tactic),
      timeRequired: getTimeRequired(index),
      difficulty: getDifficulty(index),
      cost: getCost(index)
    }))
    
    setDetailedTactics(tactics)
  }

  const getDetailedDescription = (tactic: string): string => {
    const descriptions: { [key: string]: string } = {
      'بحث السوق': 'دراسة شاملة للسوق المستهدف وتحليل المنافسين وفهم احتياجات العملاء',
      'بناء MVP': 'إنشاء نسخة أولية من المنتج تحتوي على الميزات الأساسية فقط للاختبار',
      'اختبار الفكرة': 'التحقق من صحة فكرة المشروع من خلال اختبارات حقيقية مع العملاء المحتملين',
      'بناء الجمهور': 'إنشاء قاعدة من المتابعين والعملاء المحتملين قبل الإطلاق الرسمي',
      'التحضير للإطلاق': 'الاستعداد الكامل لإطلاق المنتج أو الخدمة في السوق',
      'إطلاق رسمي': 'الإعلان الرسمي عن المنتج أو الخدمة وبدء العمليات التجارية',
      'تسويق المحتوى': 'إنشاء ونشر محتوى قيم يجذب العملاء المحتملين ويبني الثقة',
      'وسائل التواصل': 'استخدام منصات التواصل الاجتماعي للوصول للعملاء والتفاعل معهم',
      'شراكات': 'بناء علاقات تجارية مع شركات أخرى للاستفادة المتبادلة',
      'عروض الإطلاق': 'تقديم عروض خاصة ومحدودة الوقت لجذب العملاء الأوائل'
    }
    return descriptions[tactic] || 'وصف مفصل لهذا التكتيك وكيفية تطبيقه بفعالية'
  }

  const getDetailedSteps = (tactic: string): string[] => {
    const steps: { [key: string]: string[] } = {
      'بحث السوق': [
        'حدد السوق المستهدف والعملاء المحتملين',
        'ادرس المنافسين المباشرين وغير المباشرين',
        'اجمع بيانات عن حجم السوق وإمكانيات النمو',
        'اجري استطلاعات رأي مع العملاء المحتملين',
        'حلل النتائج واستخرج الرؤى المهمة'
      ],
      'بناء MVP': [
        'حدد الميزات الأساسية الضرورية فقط',
        'صمم واجهة المستخدم البسيطة',
        'طور النسخة الأولية بأقل التكاليف',
        'اختبر الوظائف الأساسية',
        'اجمع ملاحظات المستخدمين الأوائل'
      ],
      'تسويق المحتوى': [
        'حدد استراتيجية المحتوى والجمهور المستهدف',
        'أنشئ تقويم محتوى شهري',
        'اكتب مقالات ومنشورات قيمة',
        'انشر المحتوى على المنصات المناسبة',
        'قس النتائج وحسن الأداء'
      ]
    }
    return steps[tactic] || [
      'خطط للتكتيك وحدد الأهداف',
      'اجمع الموارد والأدوات المطلوبة',
      'نفذ الخطة خطوة بخطوة',
      'راقب النتائج وقس الأداء',
      'حسن وطور التكتيك حسب النتائج'
    ]
  }

  const getRequiredTools = (tactic: string): string[] => {
    const tools: { [key: string]: string[] } = {
      'بحث السوق': ['Google Forms', 'SurveyMonkey', 'Google Analytics', 'SEMrush'],
      'بناء MVP': ['Figma', 'WordPress', 'Bubble', 'GitHub'],
      'تسويق المحتوى': ['Canva', 'Buffer', 'Google Docs', 'Grammarly'],
      'وسائل التواصل': ['Facebook Business', 'Instagram', 'LinkedIn', 'Hootsuite']
    }
    return tools[tactic] || ['أدوات مجانية متاحة', 'منصات رقمية', 'أدوات تحليل']
  }

  const getTimeRequired = (index: number): string => {
    const times = ['1-2 أسابيع', '2-3 أسابيع', '1-2 أسابيع', '3-4 أسابيع', '1 أسبوع']
    return times[index] || '1-2 أسابيع'
  }

  const getDifficulty = (index: number): 'سهل' | 'متوسط' | 'صعب' => {
    const difficulties: ('سهل' | 'متوسط' | 'صعب')[] = ['سهل', 'متوسط', 'سهل', 'متوسط', 'سهل']
    return difficulties[index] || 'متوسط'
  }

  const getCost = (index: number): string => {
    const costs = ['مجاني - $50', '$50 - $200', 'مجاني - $100', '$100 - $300', 'مجاني - $50']
    return costs[index] || 'مجاني - $100'
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'سهل': return 'text-green-600 bg-green-100'
      case 'متوسط': return 'text-yellow-600 bg-yellow-100'
      case 'صعب': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحضير الخطة التفصيلية...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <>
      <Head>
        <title>الخطة التفصيلية - Growlytics</title>
        <meta name="description" content="خطة النمو التفصيلية مع الخطوات والأدوات المطلوبة" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/dashboard">
                <Button variant="outline" className="mb-4">
                  <ArrowLeftIcon className="w-4 h-4 ml-2" />
                  العودة للوحة التحكم
                </Button>
              </Link>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                📋 الخطة التفصيلية
              </h1>
              <p className="text-gray-600">
                خطوات مفصلة لتنفيذ كل تكتيك في خطة النمو الخاصة بك
              </p>
            </motion.div>

            {planSummary && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="card mb-8"
              >
                <h2 className="text-xl font-bold text-gray-900 mb-4">
                  {planSummary.title}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <CurrencyDollarIcon className="w-5 h-5 text-green-600 mx-auto mb-1" />
                    <div className="font-bold text-gray-900">${planSummary.estimated_cost}</div>
                    <div className="text-xs text-gray-600">التكلفة الإجمالية</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <ClockIcon className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                    <div className="font-bold text-gray-900">{planSummary.estimated_time_hours} ساعة</div>
                    <div className="text-xs text-gray-600">الوقت المطلوب</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <ChartBarIcon className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                    <div className="font-bold text-gray-900">{Math.round(planSummary.success_probability * 100)}%</div>
                    <div className="text-xs text-gray-600">احتمالية النجاح</div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Detailed Tactics */}
            <div className="space-y-6">
              {detailedTactics.map((tactic, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  className="card"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {index + 1}. {tactic.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {tactic.description}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(tactic.difficulty)}`}>
                        {tactic.difficulty}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <ClockIcon className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{tactic.timeRequired}</div>
                      <div className="text-xs text-gray-600">المدة المطلوبة</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <CurrencyDollarIcon className="w-5 h-5 text-green-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{tactic.cost}</div>
                      <div className="text-xs text-gray-600">التكلفة المتوقعة</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <DocumentTextIcon className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{tactic.steps.length} خطوات</div>
                      <div className="text-xs text-gray-600">للتنفيذ</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Steps */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <PlayIcon className="w-4 h-4 text-primary-600 ml-2" />
                        خطوات التنفيذ:
                      </h4>
                      <div className="space-y-2">
                        {tactic.steps.map((step, stepIndex) => (
                          <div key={stepIndex} className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                              <span className="text-xs font-medium text-primary-600">{stepIndex + 1}</span>
                            </div>
                            <span className="text-gray-700 text-sm">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tools */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <LightBulbIcon className="w-4 h-4 text-yellow-600 ml-2" />
                        الأدوات المطلوبة:
                      </h4>
                      <div className="space-y-2">
                        {tactic.tools.map((tool, toolIndex) => (
                          <div key={toolIndex} className="flex items-center space-x-2">
                            <CheckIcon className="w-4 h-4 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{tool}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="card text-center mt-8 gradient-bg text-white"
            >
              <h3 className="text-xl font-bold mb-4">
                🚀 جاهز لبدء التنفيذ؟
              </h3>
              <p className="text-white/90 mb-6">
                ابدأ بالتكتيك الأول واتبع الخطوات المفصلة. تذكر أن النجاح يحتاج للصبر والمثابرة!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/dashboard">
                  <Button variant="secondary">
                    العودة للوحة التحكم
                  </Button>
                </Link>
                <Link href="/quiz">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                    إنشاء خطة جديدة
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </Layout>
    </>
  )
}
