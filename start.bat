@echo off
echo 🚀 Starting Growlytics Development Environment...

REM Check if .env file exists
if not exist .env (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your actual API keys and configuration
    echo    Required: SUPABASE_URL, SUPABASE_ANON_KEY, OPENAI_API_KEY
    pause
)

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker first.
    echo    Visit: https://docs.docker.com/get-docker/
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    echo    Visit: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)

REM Start services with Docker Compose
echo 🐳 Starting services with Docker Compose...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo ❌ Failed to start services. Check the logs:
    docker-compose logs
    pause
    exit /b 1
)

echo ✅ Services started successfully!
echo.
echo 🌐 Application URLs:
echo    Frontend: http://localhost:3000
echo    Backend API: http://localhost:8000
echo    API Documentation: http://localhost:8000/docs
echo.
echo 📊 Database Setup:
echo    1. Go to your Supabase dashboard
echo    2. Run the SQL scripts in database/ folder:
echo       - First: database/schema.sql
echo       - Then: database/seed_tactics.sql
echo.
echo 🎯 Next Steps:
echo    1. Open http://localhost:3000 in your browser
echo    2. Complete the quiz to test the application
echo    3. Check the API docs at http://localhost:8000/docs
echo.
echo 🛑 To stop the services, run: docker-compose down
pause
