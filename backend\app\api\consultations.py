from fastapi import APIRouter, HTTPException, Depends, status
from typing import List
from datetime import datetime

from app.models.consultation import (
    ConsultationCreate, ConsultationResponse, ConsultationUpdate,
    HelpRequestCreate, HelpRequestResponse
)
from app.api.auth import get_current_user
from app.services.supabase_client import get_supabase

router = APIRouter()

@router.post("/book", response_model=ConsultationResponse)
async def book_consultation(
    consultation_data: ConsultationCreate,
    current_user: dict = Depends(get_current_user)
):
    """Book a consultation"""
    supabase = get_supabase()
    
    try:
        consultation_dict = {
            "user_id": current_user["id"],
            "consultation_type": consultation_data.consultation_type,
            "preferred_date": consultation_data.preferred_date.isoformat(),
            "alternative_dates": [d.isoformat() for d in consultation_data.alternative_dates] if consultation_data.alternative_dates else [],
            "duration_minutes": consultation_data.duration_minutes,
            "description": consultation_data.description,
            "specific_topics": consultation_data.specific_topics or [],
            "business_context": consultation_data.business_context,
            "current_challenges": consultation_data.current_challenges,
            "expected_outcomes": consultation_data.expected_outcomes,
            "status": "pending",
            "created_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table('consultations').insert(consultation_dict).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to book consultation"
            )
        
        consultation = result.data[0]
        
        return ConsultationResponse(
            id=consultation["id"],
            user_id=consultation["user_id"],
            consultation_type=consultation["consultation_type"],
            preferred_date=datetime.fromisoformat(consultation["preferred_date"]),
            duration_minutes=consultation["duration_minutes"],
            description=consultation["description"],
            specific_topics=consultation["specific_topics"],
            status=consultation["status"],
            created_at=datetime.fromisoformat(consultation["created_at"])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to book consultation: {str(e)}"
        )

@router.get("/", response_model=List[ConsultationResponse])
async def get_user_consultations(current_user: dict = Depends(get_current_user)):
    """Get user's consultations"""
    supabase = get_supabase()
    
    try:
        result = supabase.table('consultations').select('*').eq('user_id', current_user["id"]).execute()
        
        consultations = []
        for consultation in result.data:
            consultations.append(ConsultationResponse(
                id=consultation["id"],
                user_id=consultation["user_id"],
                consultation_type=consultation["consultation_type"],
                preferred_date=datetime.fromisoformat(consultation["preferred_date"]),
                confirmed_date=datetime.fromisoformat(consultation["confirmed_date"]) if consultation.get("confirmed_date") else None,
                duration_minutes=consultation["duration_minutes"],
                description=consultation["description"],
                specific_topics=consultation["specific_topics"],
                status=consultation["status"],
                created_at=datetime.fromisoformat(consultation["created_at"]),
                meeting_link=consultation.get("meeting_link"),
                rating=consultation.get("rating")
            ))
        
        return consultations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get consultations: {str(e)}"
        )

@router.post("/help-request", response_model=HelpRequestResponse)
async def create_help_request(
    help_request: HelpRequestCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a help request for a specific tactic"""
    supabase = get_supabase()
    
    try:
        help_request_dict = {
            "user_id": current_user["id"],
            "tactic_id": help_request.tactic_id,
            "subject": help_request.subject,
            "description": help_request.description,
            "priority": help_request.priority,
            "category": help_request.category,
            "status": "pending",
            "created_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table('help_requests').insert(help_request_dict).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create help request"
            )
        
        request = result.data[0]
        
        return HelpRequestResponse(
            id=request["id"],
            user_id=request["user_id"],
            tactic_id=request["tactic_id"],
            subject=request["subject"],
            description=request["description"],
            priority=request["priority"],
            status=request["status"],
            created_at=datetime.fromisoformat(request["created_at"])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create help request: {str(e)}"
        )

@router.get("/help-requests", response_model=List[HelpRequestResponse])
async def get_user_help_requests(current_user: dict = Depends(get_current_user)):
    """Get user's help requests"""
    supabase = get_supabase()
    
    try:
        result = supabase.table('help_requests').select('*').eq('user_id', current_user["id"]).execute()
        
        help_requests = []
        for request in result.data:
            help_requests.append(HelpRequestResponse(
                id=request["id"],
                user_id=request["user_id"],
                tactic_id=request["tactic_id"],
                subject=request["subject"],
                description=request["description"],
                priority=request["priority"],
                status=request["status"],
                created_at=datetime.fromisoformat(request["created_at"]),
                assigned_to=request.get("assigned_to"),
                resolution_notes=request.get("resolution_notes")
            ))
        
        return help_requests
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get help requests: {str(e)}"
        )
