import { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { motion } from 'framer-motion'
import {
  ArrowLeftIcon,
  ClockIcon,
  CheckIcon,
  PlayIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CogIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'
import { useAuth } from '@/hooks/useAuth'

interface DetailedStep {
  stepNumber: number
  title: string
  description: string
  timeEstimate: string
  difficulty: 'سهل' | 'متوسط' | 'صعب'
  instructions: string[]
  tools: {
    name: string
    purpose: string
    link?: string
    cost: 'مجاني' | 'مدفوع'
  }[]
  tips: string[]
  commonMistakes: string[]
  successCriteria: string[]
  nextSteps: string[]
}

interface TaskDetails {
  id: string
  title: string
  description: string
  totalDuration: number
  category: string
  priority: string
  difficulty: string
  overview: string
  objectives: string[]
  prerequisites: string[]
  detailedSteps: DetailedStep[]
  finalDeliverables: string[]
  qualityChecklist: string[]
}

export default function TaskDetailsPage() {
  const [taskDetails, setTaskDetails] = useState<TaskDetails | null>(null)
  const [currentStep, setCurrentStep] = useState<number>(1)
  const { user, loading } = useAuth()
  const router = useRouter()
  const { taskId } = router.query

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    if (taskId) {
      generateTaskDetails(taskId as string)
    }
  }, [user, loading, router, taskId])

  const generateTaskDetails = (id: string) => {
    const taskDetailsMap: { [key: string]: TaskDetails } = {
      'market-research': {
        id: 'market-research',
        title: 'بحث السوق الشامل',
        description: 'دراسة معمقة للسوق المستهدف وتحليل المنافسين وفهم احتياجات العملاء',
        totalDuration: 8,
        category: 'تحضير',
        priority: 'عالية',
        difficulty: 'متوسط',
        overview: 'بحث السوق هو الأساس لأي مشروع ناجح. في هذه المهمة ستتعلم كيفية دراسة السوق بطريقة علمية ومنهجية للحصول على رؤى قيمة تساعدك في اتخاذ قرارات مدروسة.',
        objectives: [
          'فهم حجم السوق المستهدف وإمكانيات النمو',
          'تحليل المنافسين المباشرين وغير المباشرين',
          'تحديد الفجوات في السوق والفرص المتاحة',
          'فهم احتياجات ومشاكل العملاء المحتملين',
          'تحديد استراتيجية التسعير المناسبة'
        ],
        prerequisites: [
          'فكرة واضحة عن المنتج أو الخدمة',
          'تحديد السوق المستهدف الأولي',
          'الوصول للإنترنت وأدوات البحث'
        ],
        detailedSteps: [
          {
            stepNumber: 1,
            title: 'تحديد السوق المستهدف',
            description: 'حدد بدقة من هم عملاؤك المحتملون وما خصائصهم',
            timeEstimate: '2 ساعة',
            difficulty: 'سهل',
            instructions: [
              'اكتب وصف مفصل لعميلك المثالي (العمر، الجنس، الدخل، الاهتمامات)',
              'حدد المنطقة الجغرافية التي تستهدفها',
              'اكتب قائمة بالمشاكل التي يواجهها عملاؤك',
              'حدد كيف يبحث عملاؤك عن حلول لمشاكلهم',
              'اعمل استطلاع رأي بسيط مع 10-20 شخص من الفئة المستهدفة'
            ],
            tools: [
              {
                name: 'Google Forms',
                purpose: 'إنشاء استطلاعات رأي',
                link: 'https://forms.google.com',
                cost: 'مجاني'
              },
              {
                name: 'SurveyMonkey',
                purpose: 'استطلاعات رأي متقدمة',
                cost: 'مدفوع'
              }
            ],
            tips: [
              '🎯 كن محدداً قدر الإمكان - "النساء العاملات 25-35 سنة" أفضل من "النساء"',
              '📊 استخدم البيانات الحقيقية وليس التخمين',
              '💬 تحدث مع عملاء محتملين حقيقيين وليس أصدقاءك فقط'
            ],
            commonMistakes: [
              'تحديد سوق واسع جداً',
              'الاعتماد على الافتراضات بدلاً من البيانات',
              'تجاهل الخصائص الديموغرافية المهمة'
            ],
            successCriteria: [
              'وصف واضح ومحدد للعميل المستهدف',
              'قائمة بـ 5-10 مشاكل رئيسية يواجهها العملاء',
              'بيانات من استطلاع رأي مع 10+ شخص'
            ],
            nextSteps: [
              'استخدم هذه المعلومات في تحليل المنافسين',
              'طور رسالتك التسويقية بناءً على مشاكل العملاء'
            ]
          },
          {
            stepNumber: 2,
            title: 'تحليل المنافسين',
            description: 'ادرس منافسيك لتفهم نقاط قوتهم وضعفهم',
            timeEstimate: '3 ساعات',
            difficulty: 'متوسط',
            instructions: [
              'اعمل قائمة بـ 5-10 منافسين مباشرين',
              'زر مواقعهم وحسابات التواصل الاجتماعي',
              'حلل أسعارهم ومنتجاتهم وخدماتهم',
              'اقرأ تقييمات العملاء عنهم',
              'حدد نقاط قوتهم وضعفهم',
              'ابحث عن الفجوات التي يمكنك ملؤها'
            ],
            tools: [
              {
                name: 'SEMrush',
                purpose: 'تحليل مواقع المنافسين',
                cost: 'مدفوع'
              },
              {
                name: 'SimilarWeb',
                purpose: 'إحصائيات المواقع',
                cost: 'مجاني'
              },
              {
                name: 'Google Alerts',
                purpose: 'متابعة أخبار المنافسين',
                cost: 'مجاني'
              }
            ],
            tips: [
              '🔍 لا تنس المنافسين غير المباشرين',
              '📱 تحقق من وجودهم على وسائل التواصل',
              '💰 قارن الأسعار والقيمة المقدمة'
            ],
            commonMistakes: [
              'التركيز على المنافسين الكبار فقط',
              'تجاهل المنافسين المحليين',
              'عدم تحليل استراتيجيات التسويق'
            ],
            successCriteria: [
              'جدول مقارنة شامل للمنافسين',
              'تحديد 3-5 فرص في السوق',
              'فهم استراتيجيات التسعير السائدة'
            ],
            nextSteps: [
              'طور ميزتك التنافسية',
              'حدد استراتيجية التسعير'
            ]
          }
        ],
        finalDeliverables: [
          'تقرير بحث السوق الشامل (5-10 صفحات)',
          'جدول تحليل المنافسين',
          'تحديد الفرص والتهديدات',
          'توصيات للخطوات التالية'
        ],
        qualityChecklist: [
          'هل البيانات مبنية على مصادر موثوقة؟',
          'هل تم تحليل 5+ منافسين على الأقل؟',
          'هل تم تحديد الفئة المستهدفة بوضوح؟',
          'هل التوصيات قابلة للتنفيذ؟'
        ]
      },
      'brand-identity': {
        id: 'brand-identity',
        title: 'تطوير الهوية البصرية',
        description: 'إنشاء هوية بصرية متميزة تعكس قيم وشخصية علامتك التجارية',
        totalDuration: 6,
        category: 'تحضير',
        priority: 'عالية',
        difficulty: 'متوسط',
        overview: 'الهوية البصرية هي وجه علامتك التجارية. ستتعلم كيفية إنشاء شعار وألوان وخطوط تعكس شخصية مشروعك وتجذب عملاءك المستهدفين.',
        objectives: [
          'تطوير شعار احترافي ومميز',
          'اختيار لوحة ألوان متناسقة',
          'تحديد الخطوط المناسبة',
          'إنشاء دليل الهوية البصرية',
          'تطبيق الهوية على جميع المواد التسويقية'
        ],
        prerequisites: [
          'فهم واضح لقيم ورسالة المشروع',
          'معرفة الفئة المستهدفة',
          'أمثلة على التصاميم المفضلة'
        ],
        detailedSteps: [
          {
            stepNumber: 1,
            title: 'تحديد شخصية العلامة التجارية',
            description: 'حدد الشخصية والقيم التي تريد أن تعكسها علامتك التجارية',
            timeEstimate: '1 ساعة',
            difficulty: 'سهل',
            instructions: [
              'اكتب 5 كلمات تصف شخصية علامتك التجارية',
              'حدد القيم الأساسية لمشروعك',
              'اكتب رسالة العلامة التجارية في جملة واحدة',
              'حدد الشعور الذي تريد أن يشعر به العملاء',
              'اجمع صور وألوان تعجبك وتناسب شخصية علامتك'
            ],
            tools: [
              {
                name: 'Pinterest',
                purpose: 'جمع الإلهام والأفكار',
                cost: 'مجاني'
              },
              {
                name: 'Behance',
                purpose: 'استكشاف تصاميم احترافية',
                cost: 'مجاني'
              }
            ],
            tips: [
              '🎨 فكر في شعورك عند رؤية علامات تجارية تحبها',
              '📝 اكتب كل شيء - حتى الأفكار الغريبة',
              '👥 فكر في كيف يريد عملاؤك أن يشعروا'
            ],
            commonMistakes: [
              'تقليد المنافسين بدلاً من التميز',
              'اختيار ألوان لأنها "جميلة" فقط',
              'تجاهل تفضيلات الفئة المستهدفة'
            ],
            successCriteria: [
              'وصف واضح لشخصية العلامة التجارية',
              'قائمة بالقيم الأساسية',
              'مجموعة من الصور الملهمة'
            ],
            nextSteps: [
              'استخدم هذه المعلومات في تصميم الشعار',
              'اختر الألوان بناءً على الشخصية المحددة'
            ]
          }
        ],
        finalDeliverables: [
          'شعار احترافي بصيغ مختلفة',
          'لوحة الألوان الرسمية',
          'دليل الهوية البصرية',
          'قوالب للمواد التسويقية'
        ],
        qualityChecklist: [
          'هل الشعار واضح في أحجام مختلفة؟',
          'هل الألوان متناسقة ومناسبة للفئة المستهدفة؟',
          'هل يمكن تطبيق الهوية على جميع المواد؟',
          'هل الهوية تعكس قيم المشروع؟'
        ]
      }
    }

    const details = taskDetailsMap[id]
    if (details) {
      setTaskDetails(details)
    } else {
      router.push('/professional-timeline')
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'سهل': return 'text-green-600 bg-green-100'
      case 'متوسط': return 'text-yellow-600 bg-yellow-100'
      case 'صعب': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading || !taskDetails) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل تفاصيل المهمة...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <>
      <Head>
        <title>{taskDetails.title} - تفاصيل المهمة</title>
        <meta name="description" content={taskDetails.description} />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/professional-timeline">
                <Button variant="outline" className="mb-4">
                  <ArrowLeftIcon className="w-4 h-4 ml-2" />
                  العودة للجدول الزمني
                </Button>
              </Link>

              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {taskDetails.title}
                  </h1>
                  <p className="text-gray-600 mb-4">{taskDetails.description}</p>
                  <div className="flex items-center gap-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(taskDetails.difficulty)}`}>
                      {taskDetails.difficulty}
                    </span>
                    <span className="flex items-center text-sm text-gray-500">
                      <ClockIcon className="w-4 h-4 ml-1" />
                      {taskDetails.totalDuration} ساعة
                    </span>
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                      {taskDetails.category}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Overview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="card mb-8"
            >
              <h2 className="text-xl font-bold text-gray-900 mb-4">نظرة عامة</h2>
              <p className="text-gray-600 mb-6">{taskDetails.overview}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">الأهداف:</h3>
                  <ul className="space-y-2">
                    {taskDetails.objectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckIcon className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-600 text-sm">{objective}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">المتطلبات المسبقة:</h3>
                  <ul className="space-y-2">
                    {taskDetails.prerequisites.map((prerequisite, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <ExclamationTriangleIcon className="w-4 h-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-600 text-sm">{prerequisite}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Step Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="card mb-8"
            >
              <h2 className="text-xl font-bold text-gray-900 mb-4">الخطوات التفصيلية</h2>
              <div className="flex flex-wrap gap-2 mb-6">
                {taskDetails.detailedSteps.map((step, index) => (
                  <button
                    key={step.stepNumber}
                    onClick={() => setCurrentStep(step.stepNumber)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      currentStep === step.stepNumber
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    الخطوة {step.stepNumber}
                  </button>
                ))}
              </div>

              {/* Current Step Details */}
              {taskDetails.detailedSteps.map((step) => (
                currentStep === step.stepNumber && (
                  <motion.div
                    key={step.stepNumber}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4 }}
                    className="space-y-6"
                  >
                    <div className="border-l-4 border-primary-600 pl-4">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-bold text-gray-900">{step.title}</h3>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(step.difficulty)}`}>
                          {step.difficulty}
                        </span>
                        <span className="flex items-center text-sm text-gray-500">
                          <ClockIcon className="w-4 h-4 ml-1" />
                          {step.timeEstimate}
                        </span>
                      </div>
                      <p className="text-gray-600">{step.description}</p>
                    </div>

                    {/* Instructions */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <PlayIcon className="w-4 h-4 text-primary-600 ml-2" />
                        التعليمات خطوة بخطوة:
                      </h4>
                      <div className="space-y-3">
                        {step.instructions.map((instruction, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                            <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                              <span className="text-xs font-medium text-primary-600">{index + 1}</span>
                            </div>
                            <span className="text-gray-700 text-sm">{instruction}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tools */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <CogIcon className="w-4 h-4 text-blue-600 ml-2" />
                        الأدوات المطلوبة:
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {step.tools.map((tool, index) => (
                          <div key={index} className="p-3 border border-gray-200 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <h5 className="font-medium text-gray-900">{tool.name}</h5>
                              <span className={`px-2 py-1 text-xs rounded ${
                                tool.cost === 'مجاني' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'
                              }`}>
                                {tool.cost}
                              </span>
                            </div>
                            <p className="text-gray-600 text-sm">{tool.purpose}</p>
                            {tool.link && (
                              <a href={tool.link} target="_blank" rel="noopener noreferrer" className="text-primary-600 text-sm hover:underline">
                                زيارة الموقع
                              </a>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tips and Common Mistakes */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                          <LightBulbIcon className="w-4 h-4 text-yellow-600 ml-2" />
                          نصائح مهمة:
                        </h4>
                        <div className="space-y-2">
                          {step.tips.map((tip, index) => (
                            <div key={index} className="p-2 bg-yellow-50 rounded text-sm text-gray-700">
                              {tip}
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                          <ExclamationTriangleIcon className="w-4 h-4 text-red-600 ml-2" />
                          أخطاء شائعة:
                        </h4>
                        <div className="space-y-2">
                          {step.commonMistakes.map((mistake, index) => (
                            <div key={index} className="p-2 bg-red-50 rounded text-sm text-gray-700">
                              ❌ {mistake}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Success Criteria */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <ChartBarIcon className="w-4 h-4 text-green-600 ml-2" />
                        معايير النجاح:
                      </h4>
                      <div className="space-y-2">
                        {step.successCriteria.map((criteria, index) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-green-50 rounded">
                            <CheckIcon className="w-4 h-4 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{criteria}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )
              ))}
            </motion.div>

            {/* Final Deliverables */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="card"
            >
              <h2 className="text-xl font-bold text-gray-900 mb-4">المخرجات النهائية</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <DocumentTextIcon className="w-4 h-4 text-blue-600 ml-2" />
                    المخرجات المطلوبة:
                  </h3>
                  <ul className="space-y-2">
                    {taskDetails.finalDeliverables.map((deliverable, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckIcon className="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-600 text-sm">{deliverable}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <CheckIcon className="w-4 h-4 text-green-600 ml-2" />
                    قائمة مراجعة الجودة:
                  </h3>
                  <ul className="space-y-2">
                    {taskDetails.qualityChecklist.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <input type="checkbox" className="mt-1" />
                        <span className="text-gray-600 text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </Layout>
    </>
  )
}