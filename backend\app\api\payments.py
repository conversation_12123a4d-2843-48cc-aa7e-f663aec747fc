from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List
import stripe
from datetime import datetime

from app.api.auth import get_current_user
from app.core.config import settings
from app.services.supabase_client import get_supabase

router = APIRouter()

# Initialize Stripe
if settings.STRIPE_SECRET_KEY:
    stripe.api_key = settings.STRIPE_SECRET_KEY

class PaymentIntent(BaseModel):
    amount: int  # Amount in cents
    currency: str = "usd"
    description: Optional[str] = None
    metadata: Optional[dict] = {}

class PaymentIntentResponse(BaseModel):
    client_secret: str
    payment_intent_id: str

class SubscriptionPlan(BaseModel):
    name: str
    price: float
    currency: str = "usd"
    interval: str = "month"  # month or year
    features: List[str]

# Subscription plans
SUBSCRIPTION_PLANS = [
    {
        "id": "basic",
        "name": "الخطة الأساسية",
        "price": 29.99,
        "currency": "usd",
        "interval": "month",
        "features": [
            "خطة نمو واحدة",
            "5 تكتيكات شهرياً",
            "دعم عبر البريد الإلكتروني",
            "مكتبة التكتيكات الأساسية"
        ]
    },
    {
        "id": "premium",
        "name": "الخطة المتقدمة",
        "price": 79.99,
        "currency": "usd",
        "interval": "month",
        "features": [
            "خطط نمو غير محدودة",
            "تكتيكات غير محدودة",
            "دعم أولوية",
            "مكتبة التكتيكات الكاملة",
            "تقارير مفصلة",
            "استشارة شهرية مجانية"
        ]
    },
    {
        "id": "enterprise",
        "name": "خطة الشركات",
        "price": 199.99,
        "currency": "usd",
        "interval": "month",
        "features": [
            "جميع ميزات الخطة المتقدمة",
            "دعم مخصص",
            "استشارات أسبوعية",
            "تدريب الفريق",
            "تكامل مخصص",
            "مدير حساب مخصص"
        ]
    }
]

@router.get("/plans")
async def get_subscription_plans():
    """Get available subscription plans"""
    return {"plans": SUBSCRIPTION_PLANS}

@router.post("/create-payment-intent", response_model=PaymentIntentResponse)
async def create_payment_intent(
    payment_data: PaymentIntent,
    current_user: dict = Depends(get_current_user)
):
    """Create a payment intent for one-time payments"""
    try:
        if not settings.STRIPE_SECRET_KEY:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Payment service not configured"
            )
        
        # Create payment intent
        intent = stripe.PaymentIntent.create(
            amount=payment_data.amount,
            currency=payment_data.currency,
            description=payment_data.description,
            metadata={
                "user_id": current_user["id"],
                "user_email": current_user["email"],
                **payment_data.metadata
            }
        )
        
        return PaymentIntentResponse(
            client_secret=intent.client_secret,
            payment_intent_id=intent.id
        )
        
    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create payment intent: {str(e)}"
        )

@router.post("/create-subscription")
async def create_subscription(
    plan_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Create a subscription for the user"""
    try:
        if not settings.STRIPE_SECRET_KEY:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Payment service not configured"
            )
        
        # Find the plan
        plan = next((p for p in SUBSCRIPTION_PLANS if p["id"] == plan_id), None)
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription plan not found"
            )
        
        # In a real implementation, you would:
        # 1. Create or get Stripe customer
        # 2. Create subscription with the selected plan
        # 3. Store subscription info in database
        
        # For now, return a mock response
        return {
            "message": f"Subscription to {plan['name']} created successfully",
            "plan": plan,
            "status": "active",
            "next_billing_date": "2024-02-01"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create subscription: {str(e)}"
        )

@router.get("/subscription")
async def get_user_subscription(current_user: dict = Depends(get_current_user)):
    """Get user's current subscription"""
    try:
        # In a real implementation, you would query the database
        # For now, return a mock response
        return {
            "plan": "basic",
            "status": "active",
            "current_period_start": "2024-01-01",
            "current_period_end": "2024-02-01",
            "cancel_at_period_end": False
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get subscription: {str(e)}"
        )

@router.post("/cancel-subscription")
async def cancel_subscription(current_user: dict = Depends(get_current_user)):
    """Cancel user's subscription"""
    try:
        # In a real implementation, you would:
        # 1. Cancel the Stripe subscription
        # 2. Update database
        # 3. Send confirmation email
        
        return {"message": "Subscription cancelled successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel subscription: {str(e)}"
        )

@router.post("/webhook")
async def stripe_webhook():
    """Handle Stripe webhooks"""
    # In a real implementation, you would:
    # 1. Verify webhook signature
    # 2. Handle different event types
    # 3. Update database accordingly
    
    return {"received": True}
