from fastapi import APIRouter, HTTPException, Depends, status, Query
from typing import List, Optional
from datetime import datetime

from app.models.tactic import (
    TacticResponse, TacticFilters, PlanTacticCreate, 
    PlanTacticUpdate, PlanTactic
)
from app.api.auth import get_current_user
from app.services.supabase_client import get_supabase

router = APIRouter()

# Sample tactics data (in a real app, this would be in the database)
SAMPLE_TACTICS = [
    {
        "id": "1",
        "title": "تحسين محركات البحث (SEO)",
        "description": "تحسين موقعك ليظهر في نتائج البحث الأولى",
        "short_description": "تحسين ظهور موقعك في جوجل",
        "tactic_type": "organic",
        "level": "intermediate",
        "cost": "low",
        "channel": "seo",
        "estimated_time_hours": 20,
        "estimated_cost_usd": 100.0,
        "tools_required": ["Google Analytics", "Google Search Console", "SEMrush"],
        "steps": [
            "بحث الكلمات المفتاحية",
            "تحسين المحتوى",
            "بناء الروابط الخارجية",
            "تحسين سرعة الموقع"
        ],
        "expected_outcome": "زيادة الزوار من محركات البحث بنسبة 50%",
        "tags": ["seo", "organic", "website"],
        "business_stages": ["launch", "growth", "scale"],
        "success_metrics": ["Organic Traffic", "Keyword Rankings", "Click-through Rate"],
        "created_at": datetime.utcnow().isoformat(),
        "is_active": True,
        "usage_count": 150,
        "rating": 4.5
    },
    {
        "id": "2",
        "title": "التسويق عبر وسائل التواصل الاجتماعي",
        "description": "بناء حضور قوي على منصات التواصل الاجتماعي",
        "short_description": "التسويق عبر السوشيال ميديا",
        "tactic_type": "organic",
        "level": "beginner",
        "cost": "free",
        "channel": "social_media",
        "estimated_time_hours": 15,
        "estimated_cost_usd": 0.0,
        "tools_required": ["Facebook", "Instagram", "Twitter", "Canva"],
        "steps": [
            "إنشاء الحسابات",
            "تطوير استراتيجية المحتوى",
            "النشر المنتظم",
            "التفاعل مع الجمهور"
        ],
        "expected_outcome": "زيادة المتابعين والتفاعل بنسبة 100%",
        "tags": ["social_media", "content", "engagement"],
        "business_stages": ["idea", "launch", "growth"],
        "success_metrics": ["Followers", "Engagement Rate", "Reach"],
        "created_at": datetime.utcnow().isoformat(),
        "is_active": True,
        "usage_count": 200,
        "rating": 4.2
    },
    {
        "id": "3",
        "title": "الإعلانات المدفوعة على جوجل",
        "description": "إنشاء حملات إعلانية مدفوعة على جوجل للحصول على زوار مستهدفين",
        "short_description": "إعلانات جوجل المدفوعة",
        "tactic_type": "paid",
        "level": "intermediate",
        "cost": "medium",
        "channel": "ppc",
        "estimated_time_hours": 10,
        "estimated_cost_usd": 500.0,
        "tools_required": ["Google Ads", "Google Analytics", "Keyword Planner"],
        "steps": [
            "بحث الكلمات المفتاحية",
            "إنشاء الحملات",
            "كتابة الإعلانات",
            "تحسين الحملات"
        ],
        "expected_outcome": "الحصول على 1000 زائر مستهدف شهرياً",
        "tags": ["ppc", "google_ads", "paid"],
        "business_stages": ["profit", "growth", "scale"],
        "success_metrics": ["Click-through Rate", "Cost per Click", "Conversion Rate"],
        "created_at": datetime.utcnow().isoformat(),
        "is_active": True,
        "usage_count": 120,
        "rating": 4.3
    }
]

@router.get("/", response_model=List[TacticResponse])
async def get_tactics(
    tactic_type: Optional[str] = Query(None),
    level: Optional[str] = Query(None),
    cost: Optional[str] = Query(None),
    channel: Optional[str] = Query(None),
    business_stage: Optional[str] = Query(None),
    search_query: Optional[str] = Query(None),
    limit: int = Query(20, le=100)
):
    """Get tactics with optional filters"""
    try:
        # In a real implementation, this would query the database
        tactics = SAMPLE_TACTICS.copy()
        
        # Apply filters
        if tactic_type:
            tactics = [t for t in tactics if t["tactic_type"] == tactic_type]
        if level:
            tactics = [t for t in tactics if t["level"] == level]
        if cost:
            tactics = [t for t in tactics if t["cost"] == cost]
        if channel:
            tactics = [t for t in tactics if t["channel"] == channel]
        if business_stage:
            tactics = [t for t in tactics if business_stage in t["business_stages"]]
        if search_query:
            search_lower = search_query.lower()
            tactics = [t for t in tactics if 
                      search_lower in t["title"].lower() or 
                      search_lower in t["description"].lower() or
                      any(search_lower in tag.lower() for tag in t["tags"])]
        
        # Limit results
        tactics = tactics[:limit]
        
        # Convert to response model
        return [TacticResponse(**tactic) for tactic in tactics]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tactics: {str(e)}"
        )

@router.get("/{tactic_id}", response_model=TacticResponse)
async def get_tactic(tactic_id: str):
    """Get specific tactic"""
    try:
        # Find tactic
        tactic = next((t for t in SAMPLE_TACTICS if t["id"] == tactic_id), None)
        
        if not tactic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tactic not found"
            )
        
        return TacticResponse(**tactic)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tactic: {str(e)}"
        )

@router.post("/add-to-plan")
async def add_tactic_to_plan(
    plan_tactic: PlanTacticCreate,
    current_user: dict = Depends(get_current_user)
):
    """Add tactic to user's growth plan"""
    supabase = get_supabase()
    
    try:
        # Verify plan belongs to user
        plan_result = supabase.table('growth_plans').select('id').eq('id', plan_tactic.plan_id).eq('user_id', current_user["id"]).execute()
        
        if not plan_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Growth plan not found"
            )
        
        # Check if tactic already exists in plan
        existing_result = supabase.table('plan_tactics').select('id').eq('plan_id', plan_tactic.plan_id).eq('tactic_id', plan_tactic.tactic_id).execute()
        
        if existing_result.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tactic already exists in this plan"
            )
        
        # Add tactic to plan
        plan_tactic_data = {
            "plan_id": plan_tactic.plan_id,
            "tactic_id": plan_tactic.tactic_id,
            "priority": plan_tactic.priority,
            "status": plan_tactic.status,
            "notes": plan_tactic.notes,
            "created_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table('plan_tactics').insert(plan_tactic_data).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add tactic to plan"
            )
        
        return {"message": "Tactic added to plan successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add tactic to plan: {str(e)}"
        )

@router.get("/plan/{plan_id}", response_model=List[PlanTactic])
async def get_plan_tactics(
    plan_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get all tactics for a specific plan"""
    supabase = get_supabase()
    
    try:
        # Verify plan belongs to user
        plan_result = supabase.table('growth_plans').select('id').eq('id', plan_id).eq('user_id', current_user["id"]).execute()
        
        if not plan_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Growth plan not found"
            )
        
        # Get plan tactics
        result = supabase.table('plan_tactics').select('*').eq('plan_id', plan_id).execute()
        
        plan_tactics = []
        for pt in result.data:
            # Get tactic details
            tactic = next((t for t in SAMPLE_TACTICS if t["id"] == pt["tactic_id"]), None)
            tactic_response = TacticResponse(**tactic) if tactic else None
            
            plan_tactics.append(PlanTactic(
                id=pt["id"],
                plan_id=pt["plan_id"],
                tactic_id=pt["tactic_id"],
                priority=pt["priority"],
                status=pt["status"],
                notes=pt.get("notes"),
                created_at=datetime.fromisoformat(pt["created_at"]),
                updated_at=datetime.fromisoformat(pt["updated_at"]) if pt.get("updated_at") else None,
                tactic=tactic_response
            ))
        
        return plan_tactics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get plan tactics: {str(e)}"
        )
