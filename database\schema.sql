-- Growlytics Database Schema for Supabase
-- Run this script in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'consultant')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    email_verified BOOLEAN DEFAULT FALSE,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE
);

-- Growth Plans table
CREATE TABLE public.growth_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    business_profile JSONB NOT NULL,
    estimated_cost DECIMAL(10,2),
    estimated_time_hours INTEGER,
    priority_score DECIMAL(3,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Tactics table
CREATE TABLE public.tactics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    short_description TEXT NOT NULL,
    tactic_type TEXT NOT NULL CHECK (tactic_type IN ('organic', 'paid', 'content', 'lead', 'reporting')),
    level TEXT NOT NULL CHECK (level IN ('beginner', 'intermediate', 'advanced')),
    cost TEXT NOT NULL CHECK (cost IN ('free', 'low', 'medium', 'high')),
    channel TEXT NOT NULL CHECK (channel IN ('social_media', 'email', 'seo', 'ppc', 'content_marketing', 'influencer', 'partnerships', 'direct', 'other')),
    estimated_time_hours INTEGER NOT NULL,
    estimated_cost_usd DECIMAL(10,2) NOT NULL,
    tools_required TEXT[] DEFAULT '{}',
    steps TEXT[] DEFAULT '{}',
    expected_outcome TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    business_stages TEXT[] DEFAULT '{}',
    success_metrics TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(2,1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Plan Tactics (many-to-many relationship)
CREATE TABLE public.plan_tactics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    plan_id UUID REFERENCES public.growth_plans(id) ON DELETE CASCADE NOT NULL,
    tactic_id UUID REFERENCES public.tactics(id) ON DELETE CASCADE NOT NULL,
    priority INTEGER DEFAULT 1,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'skipped')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(plan_id, tactic_id)
);

-- Consultations table
CREATE TABLE public.consultations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    consultant_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    consultation_type TEXT NOT NULL CHECK (consultation_type IN ('strategy', 'implementation', 'review', 'general')),
    preferred_date TIMESTAMP WITH TIME ZONE NOT NULL,
    alternative_dates TIMESTAMP WITH TIME ZONE[] DEFAULT '{}',
    confirmed_date TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER DEFAULT 60,
    description TEXT NOT NULL,
    specific_topics TEXT[] DEFAULT '{}',
    business_context TEXT,
    current_challenges TEXT,
    expected_outcomes TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show')),
    meeting_link TEXT,
    meeting_notes TEXT,
    follow_up_actions TEXT[] DEFAULT '{}',
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Help Requests table
CREATE TABLE public.help_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    tactic_id UUID REFERENCES public.tactics(id) ON DELETE CASCADE NOT NULL,
    subject TEXT NOT NULL,
    description TEXT NOT NULL,
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 5),
    category TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'resolved', 'closed')),
    assigned_to UUID REFERENCES public.users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    stripe_subscription_id TEXT UNIQUE,
    plan_id TEXT NOT NULL,
    plan_name TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('active', 'cancelled', 'past_due', 'unpaid')),
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Chat Messages table (for AI chat history)
CREATE TABLE public.chat_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    message TEXT NOT NULL,
    response TEXT NOT NULL,
    context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_growth_plans_user_id ON public.growth_plans(user_id);
CREATE INDEX idx_growth_plans_created_at ON public.growth_plans(created_at);
CREATE INDEX idx_plan_tactics_plan_id ON public.plan_tactics(plan_id);
CREATE INDEX idx_plan_tactics_tactic_id ON public.plan_tactics(tactic_id);
CREATE INDEX idx_tactics_type ON public.tactics(tactic_type);
CREATE INDEX idx_tactics_level ON public.tactics(level);
CREATE INDEX idx_tactics_cost ON public.tactics(cost);
CREATE INDEX idx_tactics_channel ON public.tactics(channel);
CREATE INDEX idx_consultations_user_id ON public.consultations(user_id);
CREATE INDEX idx_consultations_status ON public.consultations(status);
CREATE INDEX idx_help_requests_user_id ON public.help_requests(user_id);
CREATE INDEX idx_help_requests_status ON public.help_requests(status);
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_chat_messages_user_id ON public.chat_messages(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.growth_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plan_tactics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.consultations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.help_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can only see and update their own data
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Growth plans policies
CREATE POLICY "Users can view own growth plans" ON public.growth_plans
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own growth plans" ON public.growth_plans
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own growth plans" ON public.growth_plans
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own growth plans" ON public.growth_plans
    FOR DELETE USING (auth.uid() = user_id);

-- Plan tactics policies
CREATE POLICY "Users can view own plan tactics" ON public.plan_tactics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.growth_plans 
            WHERE id = plan_tactics.plan_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage own plan tactics" ON public.plan_tactics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.growth_plans 
            WHERE id = plan_tactics.plan_id AND user_id = auth.uid()
        )
    );

-- Consultations policies
CREATE POLICY "Users can view own consultations" ON public.consultations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own consultations" ON public.consultations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own consultations" ON public.consultations
    FOR UPDATE USING (auth.uid() = user_id);

-- Help requests policies
CREATE POLICY "Users can view own help requests" ON public.help_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own help requests" ON public.help_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own help requests" ON public.help_requests
    FOR UPDATE USING (auth.uid() = user_id);

-- Subscriptions policies
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "Users can view own chat messages" ON public.chat_messages
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own chat messages" ON public.chat_messages
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Tactics are public (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view tactics" ON public.tactics
    FOR SELECT USING (auth.role() = 'authenticated');

-- Functions and triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_growth_plans_updated_at BEFORE UPDATE ON public.growth_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tactics_updated_at BEFORE UPDATE ON public.tactics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_plan_tactics_updated_at BEFORE UPDATE ON public.plan_tactics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_consultations_updated_at BEFORE UPDATE ON public.consultations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_help_requests_updated_at BEFORE UPDATE ON public.help_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
