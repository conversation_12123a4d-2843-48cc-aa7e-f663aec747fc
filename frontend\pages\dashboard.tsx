import { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { motion } from 'framer-motion'
import { 
  ChartBarIcon,
  SparklesIcon,
  PlusIcon,
  ClockIcon,
  CurrencyDollarIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

import Layout from '@/components/Layout'
import Button from '@/components/ui/Button'
import { useAuth } from '@/hooks/useAuth'

interface PlanSummary {
  title: string
  description: string
  key_tactics: string[]
  estimated_cost: number
  estimated_time_hours: number
  success_probability: number
}

export default function Dashboard() {
  const [planSummary, setPlanSummary] = useState<PlanSummary | null>(null)
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    // Get plan summary from localStorage
    const storedSummary = localStorage.getItem('planSummary')
    if (storedSummary) {
      try {
        const summary = JSON.parse(storedSummary)
        setPlanSummary(summary)
      } catch (error) {
        console.error('Error parsing plan summary:', error)
      }
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <>
      <Head>
        <title>لوحة التحكم - Growlytics</title>
        <meta name="description" content="لوحة التحكم الخاصة بك في Growlytics" />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                مرحباً {user?.name || 'بك'}! 👋
              </h1>
              <p className="text-gray-600">
                إليك ملخص خطة النمو الخاصة بك والخطوات التالية
              </p>
            </motion.div>

            {planSummary ? (
              <>
                {/* Plan Summary Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="card mb-8"
                >
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">
                      {planSummary.title}
                    </h2>
                    <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                      <SparklesIcon className="w-6 h-6 text-primary-600" />
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    {planSummary.description}
                  </p>

                  {/* Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <CurrencyDollarIcon className="w-6 h-6 text-green-600 mx-auto mb-2" />
                      <div className="text-xl font-bold text-gray-900">
                        ${planSummary.estimated_cost}
                      </div>
                      <div className="text-sm text-gray-600">التكلفة المتوقعة</div>
                    </div>
                    
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <ClockIcon className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                      <div className="text-xl font-bold text-gray-900">
                        {planSummary.estimated_time_hours} ساعة
                      </div>
                      <div className="text-sm text-gray-600">الوقت المطلوب</div>
                    </div>
                    
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <ChartBarIcon className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                      <div className="text-xl font-bold text-gray-900">
                        {Math.round(planSummary.success_probability * 100)}%
                      </div>
                      <div className="text-sm text-gray-600">احتمالية النجاح</div>
                    </div>
                  </div>

                  {/* Key Tactics */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      التكتيكات الرئيسية:
                    </h3>
                    <div className="space-y-2">
                      {planSummary.key_tactics.map((tactic, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-3 p-3 bg-primary-50 rounded-lg"
                        >
                          <CheckIcon className="w-5 h-5 text-primary-600 flex-shrink-0" />
                          <span className="text-gray-800">{tactic}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>

                {/* Action Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-6"
                >
                  <div className="card text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      ابدأ التنفيذ
                    </h3>
                    <p className="text-gray-600 mb-4">
                      احصل على خطوات مفصلة لكل تكتيك
                    </p>
                    <Link href="/detailed-plan">
                      <Button className="w-full">
                        عرض الخطة التفصيلية
                      </Button>
                    </Link>
                  </div>

                  <div className="card text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      تحديث الخطة
                    </h3>
                    <p className="text-gray-600 mb-4">
                      احصل على خطة محدثة بناءً على بياناتك الحالية
                    </p>
                    <Button
                      variant="outline"
                      className="w-full mb-2"
                      onClick={() => {
                        localStorage.removeItem('planSummary')
                        window.location.reload()
                      }}
                    >
                      🔄 تحديث الخطة
                    </Button>
                  </div>

                  <div className="card text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      خطة جديدة
                    </h3>
                    <p className="text-gray-600 mb-4">
                      أنشئ خطة نمو جديدة لمشروع آخر
                    </p>
                    <Link href="/quiz">
                      <Button variant="outline" className="w-full">
                        <PlusIcon className="w-4 h-4 ml-2" />
                        إنشاء خطة جديدة
                      </Button>
                    </Link>
                  </div>
                </motion.div>
              </>
            ) : (
              /* No Plan Yet */
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="card text-center"
              >
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <ChartBarIcon className="w-8 h-8 text-gray-400" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  لم تنشئ خطة نمو بعد
                </h2>
                <p className="text-gray-600 mb-6">
                  ابدأ بإنشاء خطة نمو مخصصة لمشروعك من خلال الإجابة على بعض الأسئلة البسيطة
                </p>
                <Link href="/quiz">
                  <Button size="lg">
                    <PlusIcon className="w-5 h-5 ml-2" />
                    إنشاء خطة النمو الأولى
                  </Button>
                </Link>
              </motion.div>
            )}
          </div>
        </div>
      </Layout>
    </>
  )
}
