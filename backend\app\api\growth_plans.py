from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Optional
from datetime import datetime

from app.models.growth_plan import (
    QuizAnswers, GrowthPlanSummary, GrowthPlanCreate, 
    GrowthPlanResponse, GrowthPlanUpdate
)
from app.api.auth import get_current_user
from app.services.supabase_client import get_supabase
from app.services.ai_service import ai_service

router = APIRouter()

@router.post("/generate-summary", response_model=GrowthPlanSummary)
async def generate_growth_plan_summary(quiz_answers: QuizAnswers):
    """Generate growth plan summary from quiz answers (before registration)"""
    try:
        summary = await ai_service.generate_growth_plan(quiz_answers)
        return summary
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate growth plan: {str(e)}"
        )

@router.post("/create", response_model=GrowthPlanResponse)
async def create_growth_plan(
    quiz_answers: QuizAnswers,
    current_user: dict = Depends(get_current_user)
):
    """Create full growth plan after user registration"""
    supabase = get_supabase()
    
    try:
        # Generate AI summary
        summary = await ai_service.generate_growth_plan(quiz_answers)
        
        # Create growth plan in database
        plan_data = {
            "user_id": current_user["id"],
            "title": summary.title,
            "description": summary.description,
            "business_profile": quiz_answers.dict(),
            "estimated_cost": summary.estimated_cost,
            "estimated_time_hours": summary.estimated_time_hours,
            "priority_score": summary.success_probability,
            "created_at": datetime.utcnow().isoformat(),
            "is_active": True
        }
        
        result = supabase.table('growth_plans').insert(plan_data).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create growth plan"
            )
        
        plan = result.data[0]
        
        # TODO: Add tactics to the plan based on AI recommendations
        
        return GrowthPlanResponse(
            id=plan["id"],
            title=plan["title"],
            description=plan["description"],
            business_profile=quiz_answers,
            estimated_cost=plan["estimated_cost"],
            estimated_time_hours=plan["estimated_time_hours"],
            priority_score=plan["priority_score"],
            created_at=datetime.fromisoformat(plan["created_at"]),
            is_active=plan["is_active"],
            tactics_count=0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create growth plan: {str(e)}"
        )

@router.get("/", response_model=List[GrowthPlanResponse])
async def get_user_growth_plans(current_user: dict = Depends(get_current_user)):
    """Get all growth plans for current user"""
    supabase = get_supabase()
    
    try:
        result = supabase.table('growth_plans').select('*').eq('user_id', current_user["id"]).execute()
        
        plans = []
        for plan in result.data:
            # Get tactics count
            tactics_result = supabase.table('plan_tactics').select('id').eq('plan_id', plan["id"]).execute()
            tactics_count = len(tactics_result.data) if tactics_result.data else 0
            
            plans.append(GrowthPlanResponse(
                id=plan["id"],
                title=plan["title"],
                description=plan["description"],
                business_profile=plan["business_profile"],
                estimated_cost=plan["estimated_cost"],
                estimated_time_hours=plan["estimated_time_hours"],
                priority_score=plan["priority_score"],
                created_at=datetime.fromisoformat(plan["created_at"]),
                updated_at=datetime.fromisoformat(plan["updated_at"]) if plan.get("updated_at") else None,
                is_active=plan["is_active"],
                tactics_count=tactics_count
            ))
        
        return plans
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get growth plans: {str(e)}"
        )

@router.get("/{plan_id}", response_model=GrowthPlanResponse)
async def get_growth_plan(
    plan_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get specific growth plan"""
    supabase = get_supabase()
    
    try:
        result = supabase.table('growth_plans').select('*').eq('id', plan_id).eq('user_id', current_user["id"]).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Growth plan not found"
            )
        
        plan = result.data[0]
        
        # Get tactics count
        tactics_result = supabase.table('plan_tactics').select('id').eq('plan_id', plan["id"]).execute()
        tactics_count = len(tactics_result.data) if tactics_result.data else 0
        
        return GrowthPlanResponse(
            id=plan["id"],
            title=plan["title"],
            description=plan["description"],
            business_profile=plan["business_profile"],
            estimated_cost=plan["estimated_cost"],
            estimated_time_hours=plan["estimated_time_hours"],
            priority_score=plan["priority_score"],
            created_at=datetime.fromisoformat(plan["created_at"]),
            updated_at=datetime.fromisoformat(plan["updated_at"]) if plan.get("updated_at") else None,
            is_active=plan["is_active"],
            tactics_count=tactics_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get growth plan: {str(e)}"
        )

@router.put("/{plan_id}", response_model=GrowthPlanResponse)
async def update_growth_plan(
    plan_id: str,
    plan_update: GrowthPlanUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update growth plan"""
    supabase = get_supabase()
    
    try:
        # Check if plan exists and belongs to user
        existing_result = supabase.table('growth_plans').select('*').eq('id', plan_id).eq('user_id', current_user["id"]).execute()
        
        if not existing_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Growth plan not found"
            )
        
        # Prepare update data
        update_data = {}
        if plan_update.title is not None:
            update_data["title"] = plan_update.title
        if plan_update.description is not None:
            update_data["description"] = plan_update.description
        if plan_update.estimated_cost is not None:
            update_data["estimated_cost"] = plan_update.estimated_cost
        if plan_update.estimated_time_hours is not None:
            update_data["estimated_time_hours"] = plan_update.estimated_time_hours
        if plan_update.priority_score is not None:
            update_data["priority_score"] = plan_update.priority_score
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No data to update"
            )
        
        update_data["updated_at"] = datetime.utcnow().isoformat()
        
        # Update plan
        result = supabase.table('growth_plans').update(update_data).eq('id', plan_id).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update growth plan"
            )
        
        plan = result.data[0]
        
        # Get tactics count
        tactics_result = supabase.table('plan_tactics').select('id').eq('plan_id', plan["id"]).execute()
        tactics_count = len(tactics_result.data) if tactics_result.data else 0
        
        return GrowthPlanResponse(
            id=plan["id"],
            title=plan["title"],
            description=plan["description"],
            business_profile=plan["business_profile"],
            estimated_cost=plan["estimated_cost"],
            estimated_time_hours=plan["estimated_time_hours"],
            priority_score=plan["priority_score"],
            created_at=datetime.fromisoformat(plan["created_at"]),
            updated_at=datetime.fromisoformat(plan["updated_at"]) if plan.get("updated_at") else None,
            is_active=plan["is_active"],
            tactics_count=tactics_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update growth plan: {str(e)}"
        )

@router.delete("/{plan_id}")
async def delete_growth_plan(
    plan_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete growth plan"""
    supabase = get_supabase()
    
    try:
        # Check if plan exists and belongs to user
        existing_result = supabase.table('growth_plans').select('id').eq('id', plan_id).eq('user_id', current_user["id"]).execute()
        
        if not existing_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Growth plan not found"
            )
        
        # Soft delete (mark as inactive)
        result = supabase.table('growth_plans').update({
            "is_active": False,
            "updated_at": datetime.utcnow().isoformat()
        }).eq('id', plan_id).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete growth plan"
            )
        
        return {"message": "Growth plan deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete growth plan: {str(e)}"
        )
