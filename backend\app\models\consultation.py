from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
from enum import Enum

class ConsultationStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"

class ConsultationType(str, Enum):
    STRATEGY = "strategy"
    IMPLEMENTATION = "implementation"
    REVIEW = "review"
    GENERAL = "general"

class ConsultationBase(BaseModel):
    user_id: str
    consultant_id: Optional[str] = None
    consultation_type: ConsultationType
    preferred_date: datetime
    alternative_dates: Optional[List[datetime]] = []
    duration_minutes: int = 60
    description: str
    specific_topics: Optional[List[str]] = []
    business_context: Optional[str] = None
    current_challenges: Optional[str] = None
    expected_outcomes: Optional[str] = None

class ConsultationCreate(ConsultationBase):
    pass

class ConsultationUpdate(BaseModel):
    consultant_id: Optional[str] = None
    preferred_date: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    description: Optional[str] = None
    specific_topics: Optional[List[str]] = None
    business_context: Optional[str] = None
    current_challenges: Optional[str] = None
    expected_outcomes: Optional[str] = None
    status: Optional[ConsultationStatus] = None
    meeting_link: Optional[str] = None
    meeting_notes: Optional[str] = None
    follow_up_actions: Optional[List[str]] = None

class ConsultationInDB(ConsultationBase):
    id: str
    status: ConsultationStatus = ConsultationStatus.PENDING
    created_at: datetime
    updated_at: Optional[datetime] = None
    confirmed_date: Optional[datetime] = None
    meeting_link: Optional[str] = None
    meeting_notes: Optional[str] = None
    follow_up_actions: Optional[List[str]] = []
    rating: Optional[int] = None
    feedback: Optional[str] = None

class Consultation(ConsultationInDB):
    pass

class ConsultationResponse(BaseModel):
    id: str
    user_id: str
    consultant_id: Optional[str] = None
    consultation_type: ConsultationType
    preferred_date: datetime
    confirmed_date: Optional[datetime] = None
    duration_minutes: int
    description: str
    specific_topics: List[str]
    status: ConsultationStatus
    created_at: datetime
    meeting_link: Optional[str] = None
    rating: Optional[int] = None

# Help Request models
class HelpRequestStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    CLOSED = "closed"

class HelpRequestBase(BaseModel):
    user_id: str
    tactic_id: str
    subject: str
    description: str
    priority: int = 1  # 1-5, 5 being highest
    category: Optional[str] = None

class HelpRequestCreate(HelpRequestBase):
    pass

class HelpRequestUpdate(BaseModel):
    subject: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[int] = None
    status: Optional[HelpRequestStatus] = None
    assigned_to: Optional[str] = None
    resolution_notes: Optional[str] = None

class HelpRequestInDB(HelpRequestBase):
    id: str
    status: HelpRequestStatus = HelpRequestStatus.PENDING
    created_at: datetime
    updated_at: Optional[datetime] = None
    assigned_to: Optional[str] = None
    resolved_at: Optional[datetime] = None
    resolution_notes: Optional[str] = None

class HelpRequest(HelpRequestInDB):
    pass

class HelpRequestResponse(BaseModel):
    id: str
    user_id: str
    tactic_id: str
    subject: str
    description: str
    priority: int
    status: HelpRequestStatus
    created_at: datetime
    assigned_to: Optional[str] = None
    resolution_notes: Optional[str] = None
