from fastapi import APIRouter, HTTPException, Depends, status
from typing import List
from datetime import datetime

from app.models.user import UserResponse, UserUpdate
from app.api.auth import get_current_user
from app.services.supabase_client import get_supabase

router = APIRouter()

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user: dict = Depends(get_current_user)):
    """Get user profile"""
    return UserResponse(
        id=current_user["id"],
        email=current_user["email"],
        full_name=current_user["full_name"],
        phone=current_user.get("phone"),
        role=current_user["role"],
        status=current_user["status"],
        created_at=datetime.fromisoformat(current_user["created_at"]),
        email_verified=current_user["email_verified"],
        avatar_url=current_user.get("avatar_url")
    )

@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update user profile"""
    supabase = get_supabase()
    
    try:
        # Prepare update data
        update_data = {}
        if user_update.full_name is not None:
            update_data["full_name"] = user_update.full_name
        if user_update.phone is not None:
            update_data["phone"] = user_update.phone
        if user_update.status is not None:
            update_data["status"] = user_update.status
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No data to update"
            )
        
        update_data["updated_at"] = datetime.utcnow().isoformat()
        
        # Update user
        result = supabase.table('users').update(update_data).eq('id', current_user["id"]).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update profile"
            )
        
        updated_user = result.data[0]
        
        return UserResponse(
            id=updated_user["id"],
            email=updated_user["email"],
            full_name=updated_user["full_name"],
            phone=updated_user.get("phone"),
            role=updated_user["role"],
            status=updated_user["status"],
            created_at=datetime.fromisoformat(updated_user["created_at"]),
            email_verified=updated_user["email_verified"],
            avatar_url=updated_user.get("avatar_url")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )

@router.delete("/account")
async def delete_user_account(current_user: dict = Depends(get_current_user)):
    """Delete user account"""
    supabase = get_supabase()
    
    try:
        # In a real implementation, you might want to:
        # 1. Soft delete (mark as deleted)
        # 2. Clean up related data
        # 3. Send confirmation email
        
        result = supabase.table('users').update({
            "status": "inactive",
            "updated_at": datetime.utcnow().isoformat()
        }).eq('id', current_user["id"]).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete account"
            )
        
        return {"message": "Account deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete account: {str(e)}"
        )
