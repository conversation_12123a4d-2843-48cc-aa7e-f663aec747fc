#!/bin/bash

# Growlytics Quick Start Script
echo "🚀 Starting Growlytics Development Environment..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your actual API keys and configuration"
    echo "   Required: SUPABASE_URL, SUPABASE_ANON_KEY, OPENAI_API_KEY"
    read -p "Press Enter after updating .env file..."
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Start services with Docker Compose
echo "🐳 Starting services with Docker Compose..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Services started successfully!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Documentation: http://localhost:8000/docs"
    echo ""
    echo "📊 Database Setup:"
    echo "   1. Go to your Supabase dashboard"
    echo "   2. Run the SQL scripts in database/ folder:"
    echo "      - First: database/schema.sql"
    echo "      - Then: database/seed_tactics.sql"
    echo ""
    echo "🎯 Next Steps:"
    echo "   1. Open http://localhost:3000 in your browser"
    echo "   2. Complete the quiz to test the application"
    echo "   3. Check the API docs at http://localhost:8000/docs"
    echo ""
    echo "🛑 To stop the services, run: docker-compose down"
else
    echo "❌ Failed to start services. Check the logs:"
    docker-compose logs
fi
