import { createContext, useEffect, useState, ReactNode } from 'react'
import { useRouter } from 'next/router'
import toast from 'react-hot-toast'

interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: string
  status: string
  created_at: string
  email_verified: boolean
  avatar_url?: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, fullName: string, phone?: string) => Promise<void>
  logout: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: false,
  login: async () => {},
  register: async () => {},
  logout: async () => {},
  updateUser: () => {}
})

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Check if user is logged in on mount
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        setLoading(false)
        return
      }

      const response = await fetch('http://localhost:8000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      } else {
        localStorage.removeItem('token')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      localStorage.removeItem('token')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.detail || 'فشل في تسجيل الدخول')
      }

      localStorage.setItem('token', data.access_token)
      setUser(data.user)
      toast.success('تم تسجيل الدخول بنجاح')
      
      // Redirect to dashboard or intended page
      const redirectTo = router.query.redirect as string || '/dashboard'
      router.push(redirectTo)
    } catch (error: any) {
      toast.error(error.message || 'حدث خطأ في تسجيل الدخول')
      throw error
    }
  }

  const register = async (email: string, password: string, fullName: string, phone?: string) => {
    try {
      const response = await fetch('http://localhost:8000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          email, 
          password, 
          full_name: fullName,
          phone 
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.detail || 'فشل في إنشاء الحساب')
      }

      localStorage.setItem('token', data.access_token)
      setUser(data.user)
      toast.success('تم إنشاء الحساب بنجاح')
      
      // Redirect to dashboard
      router.push('/dashboard')
    } catch (error: any) {
      toast.error(error.message || 'حدث خطأ في إنشاء الحساب')
      throw error
    }
  }

  const logout = async () => {
    try {
      localStorage.removeItem('token')
      setUser(null)
      toast.success('تم تسجيل الخروج بنجاح')
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData })
    }
  }

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}


